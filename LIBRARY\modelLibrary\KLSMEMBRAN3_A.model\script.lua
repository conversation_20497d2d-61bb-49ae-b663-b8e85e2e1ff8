-- <PERSON>ek<PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xLimit = 247
  yLimit = 247
  xMin = 140
  yMin = 140
  
  a = 6
  g = 60
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchWidth = 2.5
  local notchDepth = 2
  local sunkenDepth = 4
  local cThinDiameter = 5
  local cMidDiameter = 10
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  
  if X<xMin or Y<yMin then        
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("H_Freze10mm_Dis")
  --Outermost notch
  G.setThickness(-2*notchDepth)
  distance = a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  --Second outer notch
  G.setThickness(-notchDepth)
  distance = a+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("Cep_Acma")		--Pocketing
  if Y>yLimit and X>xLimit then
    G.setThickness(-(3*notchDepth+sunkenDepth))
    distance = a+3*notchWidth+g+sunkenWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("H_Freze10mm_Ic")
  if Y>yLimit and X>xLimit then		--Inner notches
    G.setThickness(-notchDepth)
    distance = a+notchWidth+g
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-2*notchDepth)
    distance = a+2*notchWidth+g
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_AciliV45")
  if Y>yLimit and X>xLimit then		--Angled surfaces
    G.setThickness(-2*notchDepth)
    distance = a+3*notchWidth+g
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
  end
  
  G.setLayer("K_Freze5mm")
  if Y>yLimit and X>xLimit then		--Corner cleaning
    G.setThickness(0)
    distance = a+3*notchWidth+g+sunkenWidth
    corner1 = {distance,distance}
    corner2 = {X-distance,Y-distance}
    G.cleanCorners(corner1,corner2,3*notchDepth+sunkenDepth,cThinDiameter)
  end
  
  return true
end

require "ADekoDebugMode"
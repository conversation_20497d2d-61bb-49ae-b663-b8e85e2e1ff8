-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xLimit = 300
  yLimit = 300
  xMin = 140
  yMin = 140
  
  
  g = 40
  h = 40
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local offset = 5
  local notchWidth = 2.5
  local notchDepth = 2.5
  local sunkenDepth1 = 2.5
  local sunkenDepth2 = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth2 = sunkenDepth2*math.tan((math.pi*vMidAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-(2*notchDepth+sunkenDepth1))
  
  --Outer notches
  
  p11r = {offset,offset}
  p21r = {X-offset,Y-offset}
  G.rectangle(p11r,p21r)
    
  G.setThickness(-notchDepth)
  
  --Blocks inner notches
  
  distance = offset+sunkenWidth1+notchWidth
  p12r = {distance,distance} 
  p22r = {X-distance,Y-distance}
  G.rectangle(p12r,p22r)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-(notchDepth/2+sunkenDepth2))
  
  --firs inner notch
  
  distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2 
  p13r = {distance,distance} 
  p23r = {X-distance,Y-distance}
  G.rectangle(p13r,p23r)
    
  if X>xLimit and Y>yLimit then
  
    G.setLayer("K_Freze10mm")		--Channel cleaning
    G.setThickness(-(sunkenDepth2+notchDepth/2+sunkenDepth1+notchDepth))
  
    distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth+sunkenWidth1+cMidDiameter/2
    point1 = {distance, distance}
    point2 = {X-distance, Y-distance}
    G.rectangle(point1,point2)
    
    if h > cMidDiameter then
      distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth+sunkenWidth1+h-cMidDiameter/2
      point3 = {distance, distance}
      point4 = {X-distance, Y-distance}
      G.rectangle(point3,point4) 
    
      k = (h-cMidDiameter)/(cMidDiameter/2)
      
      for i=1, k, 1 do
        point1 = G.ptAdd(point1,{cMidDiameter/2,cMidDiameter/2})
        point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
        if point1[1]>point3[1]-cMidDiameter/2 then
          break
        end
        G.rectangle(point1,point2)
      end
    end
  else		--Pocketing
    G.setLayer("Cep_Acma")
    G.setThickness(-(notchDepth/2+sunkenDepth2+sunkenDepth1+notchDepth))
    distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth+sunkenWidth1
    p14r = {distance,distance} 
    p24r = {X-distance,Y-distance}
    G.rectangle(p14r,p24r)
  end
  
  G.setLayer("K_AciliV45")
  G.setThickness(-(notchDepth/2))
    
  distance = offset+sunkenWidth1+notchWidth+g
  
  --First inner sunkenFrame
  
  p11a = {distance,distance}
  p21a = {X-distance,Y-distance}
  corner1,corner2 =  G.sunkenFrame(p11a,p21a,sunkenDepth2,vMidAngle,vMidDiameter)
  
  --Lines
  
  p13a = {p11a[1],offset+sunkenWidth1+notchWidth}
  G.line(p13a,p11a,0)
  
  p14a = {X-p13a[1],p13a[2]}
  p24a = {X-p11a[1],p11a[2]}
  G.line(p14a,p24a,0)
    
  p15a = {p13a[1],Y-p13a[2]}
  p25a = {p11a[1],Y-p11a[2]}
  G.line(p15a,p25a,0)
    
  p23a = {p21a[1],Y-offset-sunkenWidth1-notchWidth}
  G.line(p23a,p21a,0)
  
  G.setThickness(-(notchDepth/2+sunkenDepth2))
    
  distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth
  
  --Second inner sunkenFrame
    
  p12a = {distance,distance}
  p22a = {X-distance,Y-distance}
  c3,c4 =  G.sunkenFrame(p12a,p22a,sunkenDepth1,vMidAngle,vMidDiameter)
  
  G.setThickness(-(notchDepth+sunkenDepth1))
  
  --Outermost sunkenFrame
  
  p16a = {offset,offset}
  p26a = {X-offset,Y-offset}
  G.rectangle(p16a,p26a)
  
  if X>xLimit and Y>yLimit then
  
    G.setThickness(-(sunkenDepth2+sunkenDepth1))
    
    distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth+sunkenWidth1+h
    p17a = {distance,distance}
    p27a = {X-distance,Y-distance}
    G.rectangle(p17a,p27a)
  
  end

  G.setLayer("K_Freze5mm")
  G.setThickness(0)
  
  pc1 = {corner1[1],corner1[2],0}
  pc2 = {corner2[1],corner2[2],0}
  pc3 = {c3[1],c3[2],0}
  pc4 = {c4[1],c4[2],0}
  
  --Corner cleaning
  
  G.cleanCorners(pc1,pc2,notchDepth/2+sunkenDepth2,cThinDiameter)
  G.cleanCorners(pc3,pc4,sunkenDepth2+notchDepth/2+sunkenDepth1+notchDepth,cThinDiameter)
  
  return true
  
end

require "ADekoDebugMode"
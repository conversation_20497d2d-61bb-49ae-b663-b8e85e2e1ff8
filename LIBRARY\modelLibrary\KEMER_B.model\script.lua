-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.make<PERSON>art<PERSON>hape()
  
  xMin = 140
  yMin = 140
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if X < xMin or Y < yMin then
    print("Part finalDepthimension too small")
    return true
  end
  
  local notchDepth = 2
  local finalDepth = 8
  local cMidDiameter = 10
  local bulge = G.bulge({X-a*1.5, Y-2.5*a, 0, 0}, {X/2, Y-a}, {a*1.5, Y-2.5*a})
  local point = {{X-a*1.5, Y-2.5*a, 0, bulge},{a*1.5, Y-2.5*a, 0, 0},{a*1.5, a},{X-a*1.5, a},{X-a*1.5, Y-2.5*a, 0, bulge}}
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  G.setLayer("K_Desen")
  G.setThickness(-finalDepth)
  G.rectangle ({0,0},{X,Y})
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(point)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(point, 10))
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(point)
  
  return true
end

------------------------------------------------
require "ADekoDebugMode"
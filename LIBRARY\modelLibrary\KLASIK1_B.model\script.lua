-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 60
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = finalDepth * math.tan((math.pi*vMidAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X<250 or Y<250 then
    G.error("Plate too small")
    return false
  end
  
  G.setLayer("K_AciliV45")		--Angled surface
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, a}, {X-a, Y-a}, 8, vMidAngle, vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setThickness(-finalDepth)
  G.rectangle({0,0},{X,Y})
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = a+sunkenWidth
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth-10, a+sunkenWidth-10}, {X-a-sunkenWidth+10, Y-a-sunkenWidth+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth, a+sunkenWidth}, {X-a-sunkenWidth, Y-a-sunkenWidth})

  return true
end
require "ADekoDebugMode"

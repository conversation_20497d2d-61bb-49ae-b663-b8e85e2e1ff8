
-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xLimit = 300
  yLimit = 300
  xMin = 240
  yMin = 240
  
  a = 55
  h = 20
  g = 85
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vWideAngle = 120
  local vWideDiameter = 60
  local finalDepth = 8
  local sunkenWidth = finalDepth * math.tan((math.pi*vWideAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV30")		--Angled surfaces
  G.setThickness(-finalDepth)
  G.rectangle({0,0},{X,Y})

  p1v = {a, a}
  p2v = {a+4*sunkenWidth+2*h+g, Y-a}
  p3v = {X-(a+4*sunkenWidth+2*h+g), a}
  p4v = {X-a, Y-a}
  
  if X>xLimit and Y>yLimit and p2v[1]<p3v[1] then
    G.setThickness(0)
    corner1, corner2 = G.sunkenFrame(p1v, p2v, finalDepth, vWideAngle,vWideDiameter)
    corner3, corner4 = G.sunkenFrame(p3v,p4v, finalDepth, vWideAngle,vWideDiameter)
    corner1[3] = 0
    corner2[3] = 0
    corner3[3] = 0
    corner4[3] = 0
    G.setThickness(-finalDepth)
    G.rectangle({a+sunkenWidth+h, a+sunkenWidth+h}, {a+3*sunkenWidth+h+g, Y-a-sunkenWidth-h})
    G.rectangle({X-(a+3*sunkenWidth+h+g), a+sunkenWidth+h}, {X-a-sunkenWidth-h, Y-a-sunkenWidth-h})
  else
    G.setThickness(0)
    corner1, corner2 = G.sunkenFrame({a,a},{X-a,Y-a},finalDepth,vWideAngle,vWideDiameter)
    corner1[3] = 0
    corner2[3] = 0
    G.setThickness(-finalDepth)
    G.rectangle({a+sunkenWidth+h,a+sunkenWidth+h},{X-(a+sunkenWidth+h),Y-(a+sunkenWidth+h)})
  end
  
  G.setLayer("K_Freze10mm")		--Channel cleaning
  G.setThickness(-finalDepth)
  
  k = math.floor((h-cMidDiameter)/(cMidDiameter/2))
  
  if X>xLimit and Y>yLimit and p2v[1]<p3v[1] then 
    point1 = {a+sunkenWidth+cMidDiameter/2,a+sunkenWidth+cMidDiameter/2}
    point2 = {a+3*sunkenWidth+2*h+g-cMidDiameter/2,Y-a-sunkenWidth-cMidDiameter/2}
    G.rectangle(point1, point2)
    
    point5 = {X-(a+3*sunkenWidth+2*h+g-cMidDiameter/2),a+sunkenWidth+cMidDiameter/2}
    point6 = {X-a-sunkenWidth-cMidDiameter/2,Y-a-sunkenWidth-cMidDiameter/2}
    G.rectangle(point5, point6)
    
    if h>cMidDiameter then
      point3 = {a+sunkenWidth+h-cMidDiameter/2,a+sunkenWidth+h-cMidDiameter/2}
      point4 = {a+3*sunkenWidth+h+g+cMidDiameter/2,Y-a-sunkenWidth-h+cMidDiameter/2}
      G.rectangle(point3, point4)
      point7 = {X-(a+3*sunkenWidth+h+g+cMidDiameter/2),a+sunkenWidth+h-cMidDiameter/2}
      point8 = {X-a-sunkenWidth-h+cMidDiameter/2,Y-a-sunkenWidth-h+cMidDiameter/2}
      G.rectangle(point5, point6)
      for i = 1, k ,1 do
        point1 = G.ptAdd(point1, {cMidDiameter/2,cMidDiameter/2})
        point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
        point5 = G.ptAdd(point5, {cMidDiameter/2,cMidDiameter/2})
        point6 = G.ptSubtract(point6, {cMidDiameter/2,cMidDiameter/2})
        G.rectangle(point1,point2)
        G.rectangle(point5,point6)
      end
    end
  else
    
    point1 = {a+sunkenWidth+cMidDiameter/2,a+sunkenWidth+cMidDiameter/2}
    point2 = {X-(a+sunkenWidth+cMidDiameter/2),Y-(a+sunkenWidth+cMidDiameter/2)}
    G.rectangle(point1, point2)
    
    if h>cMidDiameter then
      point3 = {a+sunkenWidth+h-cMidDiameter/2,a+sunkenWidth+h-cMidDiameter/2}
      point4 = {X-(a+sunkenWidth+h-cMidDiameter/2),Y-(a+sunkenWidth+h-cMidDiameter/2)}
      G.rectangle(point3, point4)
      
      for i = 1, k ,1 do
        point1 = G.ptAdd(point1, {cMidDiameter/2,cMidDiameter/2})
        point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
        G.rectangle(point1,point2)
      end
    end
  end
  
  G.setLayer("K_Freze5mm")		--Corner cleaning
  G.setThickness(0)
  
  G.cleanCorners(corner1,corner2,finalDepth,cThinDiameter)
  
  if X>xLimit and Y>yLimit and p2v[1]<p3v[1] then
    G.cleanCorners(corner3,corner4,finalDepth,cThinDiameter)
  end
  
  
  return true
end

require "ADekoDebugMode"

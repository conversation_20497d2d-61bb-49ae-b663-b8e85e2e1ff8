-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xLimit = 247
  yLimit = 247
  xMin = 140
  yMin = 140
  
  a = 5
  g = 50
  h = 20
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchWidth = 2.5
  local notchDepth = 1.25
  local sunkenDepth = 5
  local sunkenDepth2 = 7.5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("H_Freze10mm_Dis")
  if Y>yLimit and X>xLimit then     
    G.setThickness(-(sunkenDepth+notchDepth))
    distance = a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("H_Freze10mm_Ic")
  if Y>yLimit and X>xLimit then     
    G.setThickness(-(sunkenDepth+notchDepth))
    distance = a+sunkenWidth+g+sunkenWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_AciliV45")
  if Y>yLimit and X>xLimit then     
    G.setThickness(-sunkenDepth)
    distance = a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(0)
    distance = a+sunkenWidth+g
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    corner1,corner2 = G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
    corner1[3] = 0
    corner2[3] = 0
  end
  
  G.setLayer("K_Freze10mm")  
  if Y>yLimit and X>xLimit then
    G.setThickness(-(2*notchDepth+sunkenDepth))
    
    distance = a+sunkenWidth+g+sunkenWidth+notchWidth+cMidDiameter/2
    point1 = {distance, distance}
    point2 = {X-distance, Y-distance}
    G.rectangle(point1,point2)
    
    if h > cMidDiameter then
      distance = a+sunkenWidth+g+sunkenWidth+notchWidth+h-cMidDiameter/2
      point3 = {distance, distance}
      point4 = {X-distance, Y-distance}
      G.rectangle(point3,point4) 
    
      u = (h-cMidDiameter)/(cMidDiameter/2)
      
      for i=1, u, 1 do
        point1 = G.ptAdd(point1,{cMidDiameter/2,cMidDiameter/2})
        point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
        if point1[1]>point3[1]-cMidDiameter/2 then
          break
        end
        G.rectangle(point1,point2)
      end
    end
  end
  
  G.setLayer("K_AciliV15")
  if Y>yLimit and X>xLimit then
    G.setThickness(-(2*notchDepth+sunkenDepth))
    distance = a+2*sunkenWidth+g+notchWidth+h
    point1 = {distance, distance}
    point2 = {X-distance, Y-distance}
    G.rectangle(point1,point2)
  else
    G.setThickness(-sunkenDepth)
    distance = 0
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_Freze5mm")
  if Y>yLimit and X>xLimit then     
    G.setThickness(0)
    G.cleanCorners(corner1,corner2,sunkenDepth+notchDepth,cThinDiameter)
    distance = a+sunkenWidth+g+sunkenWidth+notchWidth
    corner3 = {distance,distance}
    corner4 = {X-distance,Y-distance}
    G.cleanCorners(corner3,corner4,2*notchDepth+sunkenDepth,cThinDiameter)
  end
  
  return true
end

require "ADekoDebugMode"
-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 50
  spacing = 40
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  finalDepth = 8
	
  if X < 150 or Y < 120 then
    print("Part dimension too small")
    return true
  end
  
  return Rustic_Regular(X, Y)
end

------------------------------------------------
function Rustic_Regular(X, Y)
	local point1 = {X-a*1.5, Y-2.5*a, 0, 0}
	local bulge = G.bulge(point1, {X/2, Y-a}, {a*1.5, Y-2.5*a})
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
  point1[4] = bulge
  local point2 = {a*1.5, Y-2.5*a, 0, 0}
  G.setLayer("K_Freze10mm")
  G.setThickness(-finalDepth)
  G.line(point1, point2, bulge)		-- make the arc
  local radius = G.radius(point1, point2, bulge)
  local sunkenDepth = G.distance(point1, point2)
  local nLines = math.floor((X-2*a*1.5)/spacing)
  local modifiedSpacing = (X-2*a*1.5)/nLines
  for i=0, nLines, 1		-- loop for the vertical lines
  do
  	local Lp1 = {a*1.5+i*modifiedSpacing, a}		-- imaginary lines to intersect the above arc
  	local Lp2 = {a*1.5+i*modifiedSpacing, Y}
  	comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		--find center of the arc
  	if (comment=='tangent' or comment=='intersection') then
  		comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		--find line-arc intersection point
  		if (comment2=='tangent' or comment2=='secant') then
         if (i==0 or i==nLines) then 
           G.setLayer("K_Freze10mm")
         else
           G.setLayer("K_Freze5mm")
         end
         G.setThickness(-finalDepth)
  			G.line(intersection1, Lp1)
  		end
  	else
  		G.error()
       return false
  	end
  end
  G.setLayer("K_Freze10mm")
  G.setThickness(-finalDepth)
  G.line({a*1.5, a}, {X-a*1.5, a})		-- close the botom
  return true
end

require "ADekoDebugMode"
<template>
  <div class="ocjs-canvas-container">
    <!-- Loading indicator -->
    <div v-if="isProcessing" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">{{ $t('ocjs.processing') }}</p>
        <p class="loading-detail">{{ processingStep }}</p>
        <div class="progress-info">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
          </div>
          <p class="progress-text">{{ progressPercentage }}% complete</p>
          <div class="tools-progress">
            <p class="tools-text">Tool Progress: {{ currentTool }}/{{ totalTools }}</p>
            <p v-if="elapsedTime > 0" class="time-text">Elapsed: {{ elapsedTime }}s</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Error display -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <h3 class="error-title">{{ $t('ocjs.error') }}</h3>
        <p class="error-message">{{ error }}</p>
        <button @click="retryProcessing" class="retry-button">
          {{ $t('ocjs.retry') }}
        </button>
      </div>
    </div>

    <!-- 3D Model Viewer -->
    <div v-if="modelUrl && !isProcessing && !error" class="model-viewer-container">
      <div
        ref="threeContainer"
        class="three-viewer-wrapper"
        :style="{ width: '100%', height: '100%' }"
      ></div>
    </div>

    <!-- Empty state -->
    <div v-if="!modelUrl && !isProcessing && !error" class="empty-state">
      <div class="empty-content">
        <h3 class="empty-title">{{ $t('ocjs.noModel') }}</h3>
        <p class="empty-message">{{ $t('ocjs.noModelDescription') }}</p>
      </div>
    </div>

    <!-- Controls overlay - always visible for debugging -->
    <div class="controls-overlay">
      <button @click="downloadGLB" class="control-button" :title="$t('ocjs.download')" :disabled="!modelUrl">
        <Download :size="16" />
      </button>
      <button @click="resetCamera" class="control-button" :title="$t('ocjs.resetCamera')" :disabled="!scene">
        <RotateCcw :size="16" />
      </button>
      <button @click="toggleAutoRotate" class="control-button" :title="$t('ocjs.toggleRotation')" :disabled="!scene">
        <RotateCw :size="16" />
      </button>
      <button @click="toggleWireframe" class="control-button" title="Toggle Wireframe" :disabled="!scene">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
        </svg>
      </button>
      <button @click="debugModel" class="control-button" title="Debug Model Info">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="16" x2="12" y2="12"/>
          <line x1="12" y1="8" x2="12.01" y2="8"/>
        </svg>
      </button>
      <button @click="testWorker" class="control-button" title="Test Worker" :disabled="isProcessing">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 12l2 2 4-4"/>
          <circle cx="12" cy="12" r="10"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// WORKER-BASED OCJS PROCESSING - v2.1 - FORCE SERVICE RELOAD: 2025-01-04-12:05
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { Download, RotateCcw, RotateCw } from 'lucide-vue-next'
import { cncToolService } from '../services/cncToolService'
import { ocjsService } from '../services/ocjsService'
import type { DrawCommand } from '../types'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

interface Props {
  drawCommands: DrawCommand[]
}

const props = withDefaults(defineProps<Props>(), {
  drawCommands: () => []
})

// Reactive state
const isProcessing = ref(false)
const processingStep = ref('')
const error = ref<string | null>(null)
const modelUrl = ref<string | null>(null)
const progressPercentage = ref(0)
const currentTool = ref(0)
const totalTools = ref(0)
const startTime = ref(0)
const elapsedTime = ref(0)
// Removed unused glbData ref

// Three.js references - DON'T make Three.js objects reactive!
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let model: THREE.Group | null = null
let animationId: number | null = null
let autoRotate = false
let wireframeMode = false
const threeContainer = ref<HTMLDivElement>()



// Process draw commands using worker service
const processDrawCommands = async () => {
  console.log('🚀 WORKER-BASED PROCESSING STARTED')

  if (!props.drawCommands || props.drawCommands.length === 0) {
    modelUrl.value = null
    return
  }

  isProcessing.value = true
  error.value = null
  startTime.value = Date.now()
  elapsedTime.value = 0

  try {
    processingStep.value = '🔍 Parsing geometry layers...'
    console.log('📊 Processing', props.drawCommands.length, 'draw commands')

    // Parse commands for OC.js processing
    const parsed = cncToolService.parseCommandsForOCJS(props.drawCommands)
    console.log('🔍 Parsed commands:', parsed)

    if (parsed.panelCommands.length === 0) {
      throw new Error('No PANEL layer found - cannot create door body')
    }

    processingStep.value = '📏 Extracting door parameters...'
    console.log('🔧 Panel commands found:', parsed.panelCommands.length)
    console.log('🔧 Top tools found:', parsed.topTools.length)
    console.log('🔧 Bottom tools found:', parsed.bottomTools.length)

    // Extract door parameters using the service
    console.log('🔧 Using OCJS Service to extract door parameters')
    const doorParams = ocjsService.extractDoorParameters(parsed.panelCommands)
    console.log('📏 Extracted door parameters:', doorParams)

    // Convert to meters for better Three.js visualization
    const doorParamsMeters = {
      width: doorParams.width / 1000,
      height: doorParams.height / 1000,
      thickness: doorParams.thickness / 1000,
      cornerRadius: doorParams.cornerRadius ? doorParams.cornerRadius / 1000 : undefined,
      offsetX: (doorParams as any).offsetX || 0,
      offsetY: (doorParams as any).offsetY || 0
    }

    console.log('Door parameters:', doorParamsMeters)
    console.log('Door size in mm: W=' + doorParams.width + ' H=' + doorParams.height + ' T=' + doorParams.thickness)

    processingStep.value = '🏭 Initializing OpenCascade.js worker...'
    
    // First, ping the worker to ensure it's responsive
    try {
      const pingSuccess = await ocjsService.pingWorker()
      if (!pingSuccess) {
        throw new Error('Worker is not responsive - please refresh the page and try again')
      }
      console.log('✅ Worker is responsive, proceeding with processing')
    } catch (error) {
      throw new Error(`Worker health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Prepare tool data for worker processing
    const topTools = parsed.topTools.map(tool => ({
      tool: tool.tool,
      commands: tool.commands,
      depth: tool.depth / 1000 // Convert to meters
    }))

    const bottomTools = parsed.bottomTools.map(tool => ({
      tool: tool.tool,
      commands: tool.commands,
      depth: tool.depth / 1000 // Convert to meters
    }))

    const totalCommands = topTools.reduce((sum, tool) => sum + tool.commands.length, 0) + 
                         bottomTools.reduce((sum, tool) => sum + tool.commands.length, 0)
    const totalToolsCount = topTools.length + bottomTools.length
    
    totalTools.value = totalToolsCount
    currentTool.value = 0
    progressPercentage.value = 0

    processingStep.value = `⚙️ Processing ${totalToolsCount} tools with ${totalCommands} operations...`
    console.log('⚙️ Sending to worker:', { doorParamsMeters, topTools, bottomTools })

    // Process everything in the worker with detailed progress tracking
    processingStep.value = '🏗️ Creating door body geometry...'
    
    // Create a progress callback to update the UI
    const progressCallback = (step: string) => {
      processingStep.value = step
      elapsedTime.value = Math.round((Date.now() - startTime.value) / 1000)
      console.log('📊 Progress:', step, `(${elapsedTime.value}s)`)
      
      // Calculate progress percentage based on step keywords
      if (step.includes('Creating door body')) {
        progressPercentage.value = 15
      } else if (step.includes('Initializing OpenCascade.js worker')) {
        progressPercentage.value = 5
      } else if (step.includes('Processing tool')) {
        // Extract tool number from step string like "Processing tool 1/3"
        const match = step.match(/Processing tool (\d+)\/(\d+)/)
        if (match) {
          const current = parseInt(match[1])
          const total = parseInt(match[2])
          currentTool.value = current
          // Tool processing is 60% of total work (10% door body, 60% tools, 30% finishing)
          progressPercentage.value = 10 + Math.round((current / total) * 60)
        } else {
          // Generic tool processing
          progressPercentage.value = 30
        }
      } else if (step.includes('Performing sweep operations')) {
        progressPercentage.value = 80
      } else if (step.includes('Exporting final result')) {
        progressPercentage.value = 90
      } else if (step.includes('Creating simple door body GLB')) {
        progressPercentage.value = 95
      } else if (step.includes('Falling back to simple door body')) {
        progressPercentage.value = 85
      } else if (step.includes('Preparing 3D visualization')) {
        progressPercentage.value = 100
      }
    }
    
    const glbData = await ocjsService.processDoorWithTools(
      doorParamsMeters,
      topTools,
      bottomTools,
      progressCallback
    )

    console.log('✅ Worker processing completed, GLB data size:', glbData.byteLength)

    processingStep.value = '🎨 Preparing 3D visualization...'

    // Create blob URL from GLB data
    const glbBlob = new Blob([glbData], { type: 'model/gltf-binary' })
    const glbUrl = URL.createObjectURL(glbBlob)

    // Clean up previous URL
    if (modelUrl.value) {
      URL.revokeObjectURL(modelUrl.value)
    }

    modelUrl.value = glbUrl
    console.log('Model URL created:', modelUrl.value)

    processingStep.value = '🖥️ Initializing Three.js renderer...'

    // Wait a bit then create Three.js viewer
    setTimeout(() => {
      if (threeContainer.value) {
        const success = initThreeJS()
        if (success) {
          loadGLBModel()
        } else {
          console.error('❌ Failed to initialize Three.js')
          error.value = 'Failed to initialize 3D renderer'
        }
      } else {
        console.error('❌ Three.js container not available after worker processing')
        error.value = '3D viewer container not available'
      }
    }, 100)

  } catch (err) {
    console.error('OCJS Worker processing error:', err)
    
    // Provide more specific error messages
    let errorMessage = 'Unknown error occurred'
    if (err instanceof Error) {
      if (err.message.includes('timeout')) {
        if (err.message.includes('90 seconds')) {
          errorMessage = 'Processing timed out after 90 seconds. This typically happens with complex geometry or many tool operations. Try reducing the number of tools or simplifying the door shape.'
        } else if (err.message.includes('ping')) {
          errorMessage = 'Worker health check timed out. The processing engine is not responding. Please refresh the page and try again.'
        } else {
          errorMessage = 'Processing timed out. The operation took too long to complete. Try with fewer tools or simpler geometry.'
        }
      } else if (err.message.includes('Worker error')) {
        errorMessage = 'Worker process failed. The geometry processing engine encountered an internal error. This may be due to invalid geometry or unsupported operations.'
      } else if (err.message.includes('not responsive')) {
        errorMessage = 'Processing engine is not responding. Please refresh the page and try again.'
      } else if (err.message.includes('No PANEL layer')) {
        errorMessage = 'No door panel found. Please ensure your drawing contains a PANEL layer defining the door outline.'
      } else if (err.message.includes('Failed to serialize')) {
        errorMessage = 'Data serialization failed. The geometry data is too complex to process.'
      } else if (err.message.includes('Worker not initialized')) {
        errorMessage = 'Processing engine not ready. Please wait a moment and try again.'
      } else if (err.message.includes('OpenCascade.js initialization timeout')) {
        errorMessage = 'Processing engine initialization timed out. This may be due to slow network or system performance. Please try again.'
      } else {
        errorMessage = err.message
      }
    }
    
    error.value = errorMessage
    progressPercentage.value = 0
    currentTool.value = 0
    totalTools.value = 0
    elapsedTime.value = 0
  } finally {
    isProcessing.value = false
    processingStep.value = ''
  }
}

// Initialize Three.js scene
const initThreeJS = () => {
  if (!threeContainer.value) {
    console.error('❌ Cannot create Three.js viewer - missing container')
    return false
  }

  // Check if container is actually in the DOM and visible
  if (!threeContainer.value.offsetParent && threeContainer.value.offsetWidth === 0) {
    console.error('❌ Three.js container is not visible or not in DOM')
    return false
  }

  console.log('✅ Initializing Three.js scene')
  console.log('📦 Container element:', threeContainer.value)
  console.log('📐 Container dimensions:', {
    width: threeContainer.value.clientWidth,
    height: threeContainer.value.clientHeight,
    offsetWidth: threeContainer.value.offsetWidth,
    offsetHeight: threeContainer.value.offsetHeight
  })

  // Clear existing content
  threeContainer.value.innerHTML = ''

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xe0e0e0)

  // Camera
  const width = threeContainer.value.clientWidth || 800
  const height = threeContainer.value.clientHeight || 600
  const aspect = width / height

  console.log('Container dimensions:', { width, height, aspect })

  camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000)
  camera.position.set(2, 1.5, 2)
  camera.lookAt(0, 0, 0)

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  // Temporarily disable shadow mapping to fix proxy error in Three.js 0.160.0
  renderer.shadowMap.enabled = false
  // renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.outputColorSpace = THREE.SRGBColorSpace
  threeContainer.value.appendChild(renderer.domElement)

  console.log('Renderer created with size:', width, 'x', height)
  console.log('Canvas element:', renderer.domElement)
  console.log('Canvas style:', renderer.domElement.style.cssText)

  // Controls
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.screenSpacePanning = false
  controls.minDistance = 0.5
  controls.maxDistance = 10
  controls.maxPolarAngle = Math.PI

  // Enhanced lighting for better sweep visualization
  const ambientLight = new THREE.AmbientLight(0x404040, 0.8)  // Increased ambient
  scene.add(ambientLight)

  const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0)  // Main light
  directionalLight1.position.set(5, 5, 5)
  directionalLight1.castShadow = false
  scene.add(directionalLight1)

  const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5)  // Fill light
  directionalLight2.position.set(-5, 3, -5)
  directionalLight2.castShadow = false
  scene.add(directionalLight2)

  const directionalLight3 = new THREE.DirectionalLight(0xffffff, 0.3)  // Back light
  directionalLight3.position.set(0, -5, 0)
  directionalLight3.castShadow = false
  scene.add(directionalLight3)

  // Test cube removed - ready for GLB model loading

  // Start animation loop
  animate()

  console.log('✅ Three.js scene initialized successfully')
  return true
}

// Load GLB model
const loadGLBModel = () => {
  if (!scene || !modelUrl.value) {
    console.error('❌ Cannot load model - missing scene or URL')
    console.log('🔍 Debug info:', { scene: !!scene, modelUrl: modelUrl.value })
    return
  }

  console.log('🔄 Loading GLB model:', modelUrl.value)
  processingStep.value = 'Loading 3D model...'

  const loader = new GLTFLoader()
  loader.load(
    modelUrl.value,
    (gltf) => {
      console.log('GLB model loaded successfully')
      processingStep.value = 'Finalizing 3D visualization...'


      // Remove existing model if any
      if (model) {
        scene!.remove(model)
      }

      model = gltf.scene
      scene!.add(model)

      // Disable shadows for all meshes (shadows disabled due to Three.js proxy bug)
      model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = false
          child.receiveShadow = false
        }
      })

      // Calculate bounding box and center the model
      const box = new THREE.Box3().setFromObject(model)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())

      console.log('Model bounding box:', {
        center: center,
        size: size,
        min: box.min,
        max: box.max
      })

      // Center the model
      model.position.sub(center)

      // Adjust camera position based on model size
      const maxDim = Math.max(size.x, size.y, size.z)
      console.log('Max dimension:', maxDim)

      // Ensure minimum distance for very small models
      const distance = Math.max(maxDim * 3, 1.0) // Minimum 1 unit distance
      console.log('Camera distance:', distance)

      camera!.position.set(distance, distance * 0.75, distance)
      camera!.lookAt(0, 0, 0)

      if (controls) {
        controls.target.set(0, 0, 0)
        controls.update()
      }

      // Bounding box helper removed - model bounds are working correctly

      console.log('Model centered and camera adjusted')
      console.log('Camera position:', camera!.position)
      console.log('Model position:', model.position)

      // Clear processing state - model is ready
      isProcessing.value = false
      processingStep.value = ''
      progressPercentage.value = 0
      currentTool.value = 0
      totalTools.value = 0
      elapsedTime.value = 0
    },
    (progress) => {
      console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%')
    },
    (error) => {
      console.error('Error loading GLB model:', error)
    }
  )
}

// Animation loop
let frameCount = 0
const animate = () => {
  animationId = requestAnimationFrame(animate)

  if (controls) {
    controls.update()
  }

  // Auto rotate
  if (autoRotate && model) {
    model.rotation.y += 0.005
  }

  if (renderer && scene && camera) {
    try {
      // Workaround for Three.js 0.160.x proxy issue with modelViewMatrix
      // Force update matrices before rendering
      scene.updateMatrixWorld(true)
      camera.updateMatrixWorld(true)

      renderer.render(scene, camera)

      // Debug every 60 frames (roughly once per second)
      frameCount++
      if (frameCount % 60 === 0) {
       //console.log('Rendering frame', frameCount, 'Scene children:', scene.children.length)
      }
    } catch (error) {
      console.error('Render error (Three.js proxy issue):', error)
      // Continue animation loop despite render errors
    }
  }
}

// Control functions
const resetCamera = () => {
  if (!camera || !controls || !model) return

  // Calculate bounding box and reset camera
  const box = new THREE.Box3().setFromObject(model)
  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  const distance = maxDim * 2

  camera.position.set(distance, distance * 0.75, distance)
  camera.lookAt(0, 0, 0)
  controls.target.set(0, 0, 0)
  controls.update()

  console.log('Camera reset to default position')
}

const toggleAutoRotate = () => {
  autoRotate = !autoRotate
  console.log('Auto rotate:', autoRotate)
}

const toggleWireframe = () => {
  if (!model) return

  wireframeMode = !wireframeMode
  model.traverse((child) => {
    if (child instanceof THREE.Mesh && child.material) {
      if (Array.isArray(child.material)) {
        child.material.forEach(mat => {
          if (mat instanceof THREE.MeshStandardMaterial) {
            mat.wireframe = wireframeMode
          }
        })
      } else if (child.material instanceof THREE.MeshStandardMaterial) {
        child.material.wireframe = wireframeMode
      }
    }
  })
  console.log('Wireframe mode:', wireframeMode)
}

const debugModel = () => {
  console.log('=== THREE.JS DEBUG INFO ===')
  console.log('Scene:', scene)
  console.log('Camera:', camera)
  console.log('Renderer:', renderer)
  console.log('Model:', model)
  console.log('Container:', threeContainer.value)

  if (threeContainer.value) {
    console.log('Container dimensions:', {
      width: threeContainer.value.clientWidth,
      height: threeContainer.value.clientHeight
    })
  }

  if (renderer) {
    console.log('Renderer size:', renderer.getSize(new THREE.Vector2()))
  }

  if (scene) {
    console.log('Scene children:', scene.children.length)
    scene.children.forEach((child, index) => {
      console.log(`Child ${index}:`, child.type, child)
    })
  }

  if (model && scene && camera) {
    const box = new THREE.Box3().setFromObject(model)
    const size = box.getSize(new THREE.Vector3())
    const center = box.getCenter(new THREE.Vector3())

    console.log('Model info:')
    console.log('- Size:', size)
    console.log('- Center:', center)
    console.log('- Position:', model.position)
    console.log('- Camera position:', camera.position)
    console.log('- Camera looking at:', controls?.target || 'unknown')

    // Check if model has any meshes
    let meshCount = 0
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        meshCount++
        console.log('Mesh found:', child.geometry, child.material)
      }
    })
    console.log('Total meshes in model:', meshCount)
  }
  console.log('=== END DEBUG INFO ===')
}

// Handle window resize
const handleResize = () => {
  if (!camera || !renderer || !threeContainer.value) return

  const width = threeContainer.value.clientWidth
  const height = threeContainer.value.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// Retry processing
const retryProcessing = () => {
  error.value = null
  progressPercentage.value = 0
  currentTool.value = 0
  totalTools.value = 0
  startTime.value = 0
  elapsedTime.value = 0
  processingStep.value = ''
  console.log('🔄 Retrying processing...')
  processDrawCommands()
}

// Download GLB file
const downloadGLB = () => {
  if (!modelUrl.value) return

  const link = document.createElement('a')
  link.href = modelUrl.value
  link.download = 'door_model.glb'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}



// Watch for draw commands changes
watch(() => props.drawCommands, () => {
  processDrawCommands()
}, { immediate: true, deep: true })

// Test worker connectivity
const testWorker = async () => {
  console.log('🧪 Testing OCJS Worker connectivity...')
  try {
    isProcessing.value = true
    processingStep.value = '🧪 Testing worker connectivity...'
    progressPercentage.value = 0
    error.value = null
    
    console.log('📞 Calling ocjsService.testWorker()')

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Worker test timeout after 10 seconds')), 10000)
    })

    const testPromise = ocjsService.testWorker()
    const isWorking = await Promise.race([testPromise, timeoutPromise]) as boolean

    if (isWorking) {
      console.log('✅ OCJS Worker is working correctly')
      processingStep.value = '✅ Worker test successful'
      progressPercentage.value = 100
      setTimeout(() => {
        processingStep.value = ''
        progressPercentage.value = 0
      }, 2000)
    } else {
      console.error('❌ OCJS Worker test failed')
      error.value = 'Worker test failed - the processing engine is not responding correctly'
    }
    
    return isWorking
  } catch (err) {
    console.error('❌ OCJS Worker test error:', err)
    error.value = err instanceof Error ? err.message : 'Worker test failed'
    return false
  } finally {
    isProcessing.value = false
  }
}

// Component lifecycle
onMounted(() => {
  console.log('OCJSCanvas mounted')
  window.addEventListener('resize', handleResize)

  // Initialize Three.js immediately if we have a container
  setTimeout(() => {
    if (threeContainer.value && !scene) {
      console.log('🔧 Force initializing Three.js on mount')
      const success = initThreeJS()
      if (success) {
        console.log('✅ Three.js initialized on mount')
      } else {
        console.log('❌ Failed to initialize Three.js on mount')
      }
    }
  }, 500)
})

const fitToView = () => {
  if (!scene || !camera || !model) return

  // Calculate bounding box of the model
  const box = new THREE.Box3().setFromObject(model)

  if (!box.isEmpty()) {
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())
    const maxDim = Math.max(size.x, size.y, size.z)
    const distance = maxDim * 2

    camera.position.set(distance, distance * 0.75, distance)
    camera.lookAt(center)

    if (controls) {
      controls.target.copy(center)
      controls.update()
    }
  }
}

const toggleOrthographic = () => {
  // OCJSCanvas uses perspective camera only for now
  // This method is kept for compatibility but doesn't switch camera types
  console.log('Orthographic toggle not implemented in OCJSCanvas')
}

const setView = (viewType: 'front' | 'back' | 'top' | 'right' | 'iso') => {
  if (!camera || !controls || !model) return

  const box = new THREE.Box3().setFromObject(model)
  const center = box.getCenter(new THREE.Vector3())
  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  const distance = maxDim * 2

  let position: THREE.Vector3

  switch (viewType) {
    case 'front':
      position = new THREE.Vector3(center.x, center.y, center.z + distance)
      break
    case 'back':
      position = new THREE.Vector3(center.x, center.y, center.z - distance)
      break
    case 'top':
      position = new THREE.Vector3(center.x, center.y + distance, center.z)
      break
    case 'right':
      position = new THREE.Vector3(center.x + distance, center.y, center.z)
      break
    case 'iso':
    default:
      position = new THREE.Vector3(center.x + distance * 0.7, center.y + distance * 0.7, center.z + distance * 0.7)
      break
  }

  camera.position.copy(position)
  camera.lookAt(center)
  controls.target.copy(center)
  controls.update()
}

// Expose methods for parent component
defineExpose({
  resetCamera,
  fitToView,
  toggleWireframe,
  toggleOrthographic,
  setView,
  toggleAutoRotate,
  debugModel,
  downloadGLB,
  retryProcessing
})

onUnmounted(() => {
  console.log('OCJSCanvas unmounted')

  // Clean up Three.js
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.dispose()
  }

  window.removeEventListener('resize', handleResize)

  // Clean up blob URL
  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }

  // Note: We don't dispose the worker service here as it's a singleton
  // and may be used by other components
})
</script>

<style scoped>
.ocjs-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-content {
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.loading-detail {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.progress-info {
  margin-top: 1rem;
  width: 100%;
  max-width: 300px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
}

.tools-progress {
  margin-top: 0.5rem;
}

.tools-text {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
}

.time-text {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
  margin-top: 0.25rem;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  padding: 2rem;
  max-width: 400px;
}

.error-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-message {
  color: #374151;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #2563eb;
}

.model-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.controls-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 5;
}

.control-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button:hover:not(:disabled) {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.empty-content {
  text-align: center;
  max-width: 300px;
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.empty-message {
  color: #6b7280;
  line-height: 1.5;
}

/* Three.js viewer styling */
.three-viewer-wrapper {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

.three-viewer-wrapper canvas {
  display: block;
  width: 100%;
  height: 100%;
}

/* Progress bar styles */
.progress-info {
  margin-top: 1rem;
  width: 100%;
  max-width: 300px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-align: center;
}
</style>

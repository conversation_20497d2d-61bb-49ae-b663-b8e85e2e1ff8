-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()

  xMin = 140
  yMin = 140
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2
  local sunkenDepth = 4
  local cThickDiameter = 20
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)

  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_AciliV45")		--Angled surfaces
  G.setThickness(-sunkenDepth)
  distance1 = {0,0}
  distance2 = {X,Y}
  G.rectangle(distance1,distance2)
  
  G.setThickness(0)
  distance = sunkenWidth+a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  corner1, corner2 = G.sunkenFrame(point1,point2,2.5*sunkenDepth,vMidAngle,vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setLayer("K_Freze20mm")		--Notches and cleaning
  G.setThickness(-notchDepth)
  point1 = {sunkenWidth+a,sunkenWidth+a+cThickDiameter/2,0,0}
  point2 = ADekoLib.ptAdd(point1,{0,Y-2*(sunkenWidth+a)-cThickDiameter,0})
  point2[4] = -math.tan(math.pi/8)
  point3 = ADekoLib.ptAdd(point2,{cThickDiameter/2,cThickDiameter/2,0})
  point4 = ADekoLib.ptAdd(point3,{X-2*(sunkenWidth+a+cThickDiameter/2),0,0})
  point4[4] = -math.tan(math.pi/8)
  point5 = ADekoLib.ptAdd(point4,{cThickDiameter/2,-cThickDiameter/2,0})
  point6 = ADekoLib.ptAdd(point5,{0,-Y+2*(sunkenWidth+a+cThickDiameter/2),0})
  point6[4] = -math.tan(math.pi/8)
  point7 = ADekoLib.ptAdd(point6,{-cThickDiameter/2,-cThickDiameter/2,0})
  point8 = ADekoLib.ptAdd(point7,{-X+2*(sunkenWidth+a+cThickDiameter/2),0,0})
  point8[4] = -math.tan(math.pi/8)
  G.polyline(point1,point2,point3,point4,point5,point6,point7,point8,point1)
  
  G.setLayer("Cep_Acma")
  G.setThickness(-(2.5*sunkenDepth))
  distance = 3.5*sunkenWidth+a
  point9 = {distance,distance}
  pa = {X-distance,Y-distance}
  G.rectangle(point9,pa)

  G.setLayer("K_Freze5mm")
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,2.5*sunkenDepth,cThinDiameter)
  
  return true
end

require "ADekoDebugMode"
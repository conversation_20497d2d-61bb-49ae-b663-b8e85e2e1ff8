-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.make<PERSON>art<PERSON>hape()
  
  xMin = 140
  yMin = 140
  xLimit = 247
  yLimit = 247
  
  a = 50
  h = 10
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2
  local notchWidth = 5
  local finalDepth = 8
  local cMidDiameter = 10
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenWidth = finalDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  local bulge = G.bulge({X-notchWidth-a, Y-notchWidth-1.5*a, 0, 0}, {X/2, Y-notchWidth-a}, {a, Y-notchWidth-1.5*a})
  local points = {{X-notchWidth-a, Y-notchWidth-1.5*a, 0, bulge},{notchWidth+a, Y-notchWidth-1.5*a, 0, 0},{notchWidth+a, notchWidth+1.5*a, 0, bulge},{X-notchWidth-a, notchWidth+1.5*a},{X-notchWidth-a, Y-notchWidth-1.5*a, 0, bulge}}
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-notchDepth)
  G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})
  
  G.setLayer("K_AciliV30")
  if Y>yLimit and X>xLimit then
    G.setThickness(-finalDepth)
    G.sunkenFrameAny(points, 30,finalDepth,vWideAngle,vWideDiameter)
    G.polylineimp (G.offSet(points, -(sunkenWidth+h)))
  end
  
  G.setLayer("K_Freze10mm")
  if Y>yLimit and X>xLimit then
    G.setThickness(-finalDepth)
    point1 = G.offSet(points, -(sunkenWidth+cMidDiameter/2))
    G.polylineimp(point1)
    
    if h > cMidDiameter then
      point2 = G.offSet(points, -(sunkenWidth+h-cMidDiameter/2))
      G.polylineimp(point2)
      
      k = (h-cMidDiameter)/(cMidDiameter/2)
      
      for i=1, k, 1 do
        point1 = G.offSet(point1, -cMidDiameter/2)
        if point1[2][1]>point2[2][1]-cMidDiameter/2 then
          break
        end
        G.polylineimp(point1)
      end
    end
  end
  return true
end

------------------------------------------------
require "ADekoDebugMode"
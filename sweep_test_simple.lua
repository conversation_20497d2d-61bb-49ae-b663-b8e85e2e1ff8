-- Simple Sweep Operation Test
-- This script creates basic geometry to test the sweep operation functionality

-- Initialize ADekoLib
ADekoLib.start()
ADekoLib.showPoints(true)
ADekoLib.enableListing(true)

-- Set panel dimensions
ADekoLib.makePart(100, 100)
print("=== Simple Sweep Operation Test ===")

-- Create the door panel (base geometry)
print("Creating door panel...")
ADekoLib.setLayer("PANEL")
ADekoLib.rect("door", 0, 0, 100, 80)

-- Create a simple rectangular cut (8mm endmill)
print("Creating rectangular cut...")
ADekoLib.layer("8MM")
ADekoLib.rect("rect_cut", 20, 20, 60, 20)

-- Create a circular pocket (6mm endmill)
print("Creating circular pocket...")
ADekoLib.layer("6MM")
ADekoLib.circle("circle_cut", 50, 50, 10)

-- Create a line cut (4mm endmill)
print("Creating line cut...")
ADekoLib.layer("4MM")
ADekoLib.line("line_cut", 10, 60, 90, 60)

-- Create drilling holes (3mm drill)
print("Creating drill holes...")
ADekoLib.layer("3MM")
ADekoLib.circle("hole1", 25, 25, 1.5)
ADekoLib.circle("hole2", 75, 25, 1.5)

print("Geometry created successfully!")
print("- Door panel: 100x80x18mm")
print("- Rectangular cut: 8mm endmill")
print("- Circular pocket: 6mm endmill") 
print("- Line cut: 4mm endmill")
print("- Drill holes: 3mm drill")

-- Finish the model
ADekoLib.finish()

print("\n=== Test Instructions ===")
print("1. Load this script in the application")
print("2. Run the script to generate geometry")
print("3. Switch to 3D visualization")
print("4. The OCJS worker should automatically:")
print("   - Create door body from PANEL layer")
print("   - Generate tool BReps for each tool type")
print("   - Perform sweep operations (subtract tools from door)")
print("5. Check console for sweep operation messages")
print("6. Verify 3D model shows material removal")

print("\nExpected Results:")
print("- Door body should be created as a 100x80x18mm box")
print("- Tools should be detected: 8MM, 6MM, 4MM, 3MM")
print("- Sweep operations should subtract tool volumes from door")
print("- Final 3D model should show machined door with cuts and holes")

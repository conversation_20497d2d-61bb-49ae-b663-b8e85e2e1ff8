-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()

  a = 75
	
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local vNarrowAngle = 60
  local vNarrowDiameter = 30
  local sunkenWidth = finalDepth*math.tan((math.pi*vNarrowAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  G.setLayer("K_AciliV60")
  G.setThickness(-finalDepth)
  G.line({a+sunkenWidth, 0}, {a+sunkenWidth, Y})
  G.line({X-a-sunkenWidth, 0}, {X-a-sunkenWidth, Y})
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  G.rectangle({a+sunkenWidth,a+sunkenWidth},{X-a-sunkenWidth,Y-a-sunkenWidth})
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth-10, a+sunkenWidth-10}, {X-a-sunkenWidth+10, Y-a-sunkenWidth+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth, a+sunkenWidth}, {X-a-sunkenWidth, Y-a-sunkenWidth})
  
  return true
end

require "ADekoDebugMode"
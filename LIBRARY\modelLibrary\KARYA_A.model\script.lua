-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 50
  h = 30
  
  G = ADekoLib
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local sunkenDepth = 6
  local notchWidth = 2
  local notchDepth = 2
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vWideAngle/180)/2)
  local sunkenWidth2 = (sunkenDepth+notchDepth)*math.tan((math.pi*vWideAngle/180)/2)
  local cMidDiameter = 10
  local cThinDiameter = 5

  if X<250 or Y<250 then
    print("Part dimension too small")
    return true
  end
  
  if h<cThinDiameter or h<cMidDiameter then
    print("Tool too large")
    return true
  end
  
  G.set<PERSON>ayer("K_AciliV30")  -- V shaped deep with large angle 
  G.setThickness(-(sunkenDepth+notchDepth))
  G.rectangle({0,0},{X,Y})
  
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({sunkenWidth2+a, sunkenWidth2+a}, {X-a-sunkenWidth2, Y-a-sunkenWidth2}, sunkenDepth+notchDepth, vWideAngle, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  -- Middle angled surface
  G.setThickness(-(sunkenDepth))
  G.rectangle({a+2*sunkenWidth2+h+notchWidth, a+2*sunkenWidth2+h+notchWidth}, {X-(a+2*sunkenWidth2+h+notchWidth), Y-(a+2*sunkenWidth2+h+notchWidth)})
  
  G.setThickness(-(notchDepth/2))
  G.rectangle({2*sunkenWidth2+a+sunkenWidth+h+notchWidth, 2*sunkenWidth2+a+sunkenWidth+h+notchWidth}, {X-(2*sunkenWidth2+a+sunkenWidth+h+notchWidth), Y-(2*sunkenWidth2+a+sunkenWidth+h+notchWidth)})
  
  G.setLayer("K_Freze10mm")  -- DEEP cleanup
  G.setThickness(-(sunkenDepth+notchDepth))
  distance = a+2*sunkenWidth2+cMidDiameter/2
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  if h > cMidDiameter then
    distance = a+2*sunkenWidth2+h-cMidDiameter/2
    point3 = {distance, distance}
    point4 = {X-distance, Y-distance}
    G.rectangle(point3,point4)
    k = (h-cMidDiameter)/(cMidDiameter/2)
    for i=1, k, 1 do
      point1 = G.ptAdd(point1,{cMidDiameter/2,cMidDiameter/2})
      point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
      if point1[1]>point3[1]-cMidDiameter/2 then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setThickness(-(sunkenDepth))
  distance = a+2*sunkenWidth2+h+notchWidth-cMidDiameter/2
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_Freze5mm")
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,sunkenDepth+notchDepth,cThinDiameter)
  return true
end

------------------------------------------------

require "ADekoDebugMode"

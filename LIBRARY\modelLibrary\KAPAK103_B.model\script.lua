-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xMin = 140
  yMin = 140
  formtool = 0
  
  g = 50
  h = 30
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2.5
  local sunkenDepth1 = 2.5
  local finalDepth = 10
  local sunkenDepth3 = 7
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_AciliV45")
  G.setThickness(-sunkenDepth1)
  point1 = {g+sunkenWidth1,0}
  point2 = {g+sunkenWidth1,Y}
  G.line(point1,point2)
  
  point1 = {X-(g+sunkenWidth1),0}
  point2 = {X-(g+sunkenWidth1),Y}
  G.line(point1,point2)
  
  if formtool == 0 then
    G.setLayer("K_Desen")
  end
  
  G.setThickness(-finalDepth)
  point1 = {0,0}
  point2 = {X,Y}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = g+sunkenWidth1
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  return true
end

require "ADekoDebugMode"
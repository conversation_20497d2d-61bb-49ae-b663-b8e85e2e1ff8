-- <PERSON>ek<PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.make<PERSON>art<PERSON>hape()
  
  xLimit = 247
  yLimit = 247
  xMin = 140
  yMin = 140
  formTool = 0
  
  g = 60
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 1 
  local sunkenDepth = 1
  local notchWidth = 1.5
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if formTool == 0 then
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("H_Freze10mm_Dis")
    --Outer notches
    G.setThickness(-(5*sunkenDepth+3*notchDepth))
    distance = sunkenWidth+notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(4*sunkenDepth+3*notchDepth))
    distance = 2*sunkenWidth+2*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(3*sunkenDepth+2*notchDepth))
    distance = 3*sunkenWidth+3*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(2*sunkenDepth+notchDepth))
    distance = 4*sunkenWidth+4*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(sunkenDepth+notchDepth))
    distance = 5*sunkenWidth+5*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setLayer("K_AciliV45")
    G.setThickness(-(6*sunkenDepth+3*notchDepth))
    -- Outer angled surfaces
    distance = 0
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(5*sunkenDepth+3*notchDepth))
    distance = sunkenWidth+notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(4*sunkenDepth+2*notchDepth))
    distance = 2*sunkenWidth+2*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(3*sunkenDepth+notchDepth))
    distance = 3*sunkenWidth+3*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(2*sunkenDepth+notchDepth))
    distance = 4*sunkenWidth+4*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(sunkenDepth))
    distance = 5*sunkenWidth+5*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    if Y>yLimit and X>xLimit then		--Inner notches and corner cleaning
      G.setLayer("Cep_Acma")
      G.setThickness(-(7*sunkenDepth+2*notchDepth))
      distance = 13*sunkenWidth+8*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.rectangle(point1,point2)
      
      G.setLayer("H_Freze10mm_Ic")
      G.setThickness(-(sunkenDepth+notchDepth))
      distance = 7*sunkenWidth+5*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.rectangle(point1,point2)
      
      G.setThickness(-(3*sunkenDepth+notchDepth))
      distance = 9*sunkenWidth+6*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.rectangle(point1,point2)
      
      G.setThickness(-(6*sunkenDepth+2*notchDepth))
      distance = 12*sunkenWidth+7*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.rectangle(point1,point2)
      
      G.setLayer("K_AciliV45")
      G.setThickness(0)
      distance = 6*sunkenWidth+5*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
      
      G.setThickness(-(sunkenDepth+notchDepth))
      distance = 7*sunkenWidth+6*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.sunkenFrame(point1,point2,2*sunkenDepth,vMidAngle,vMidDiameter)
      
      G.setThickness(-(3*sunkenDepth+notchDepth))
      distance = 9*sunkenWidth+7*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.sunkenFrame(point1,point2,3*sunkenDepth,vMidAngle,vMidDiameter)
      
      G.setThickness(-(6*sunkenDepth+2*notchDepth))
      distance = 12*sunkenWidth+8*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      c1,c2 = G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
      
      G.setLayer("K_Freze5mm")
      G.setThickness(0)
      distance = 7*sunkenWidth+5*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.cleanCorners(point1,point2,sunkenDepth+notchDepth,cThinDiameter)
      
      G.setThickness(0)
      distance = 9*sunkenWidth+6*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.cleanCorners(point1,point2,3*sunkenDepth+notchDepth,cThinDiameter)
      
      G.setThickness(0)
      distance = 12*sunkenWidth+7*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.cleanCorners(point1,point2,6*sunkenDepth+2*notchDepth,cThinDiameter)
      
      G.setThickness(0)
      distance = 13*sunkenWidth+8*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.cleanCorners(point1,point2,7*sunkenDepth+2*notchDepth,cThinDiameter)
    end
  else
    G.setLayer("K_Desen")
    G.setThickness(-(6*sunkenDepth+3*notchDepth))
    distance = 0
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)  
    
    if Y>yLimit and X>xLimit then		--Pocketing and corner cleaning
      G.setLayer("Cep_Acma")
      G.setThickness(-(7*sunkenDepth+2*notchDepth))
      distance = 13*sunkenWidth+8*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.rectangle(point1,point2)
      
      G.setLayer("K_Desen")
      G.setThickness(-(7*sunkenDepth+2*notchDepth))
      distance = 13*sunkenWidth+8*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.rectangle(point1,point2)
      
      G.setLayer("K_Freze5mm")
      G.setThickness(0)
      distance = 13*sunkenWidth+8*notchWidth+g
      point1 = {distance,distance}
      point2 = {X-distance,Y-distance}
      G.cleanCorners(point1,point2,7*sunkenDepth+2*notchDepth,cThinDiameter)
    end
  end
  return true
end

require "ADekoDebugMode"
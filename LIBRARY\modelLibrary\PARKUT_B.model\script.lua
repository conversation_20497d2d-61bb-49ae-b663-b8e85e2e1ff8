-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local windowDepthFront = 14
  local windowDepthBack = 6
	
  if X < 150 or Y < 120 then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  G.rectangle({a, a}, {X-a, Y-a})
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-10, a-10}, {X-a+10, Y-a+10})
  
  <PERSON><PERSON>setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a, a}, {X-a, Y-a})
  return true
end

require "ADekoDebugMode"
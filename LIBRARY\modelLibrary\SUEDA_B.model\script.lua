-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  xMin = 350
  yMin = 350
  
  c = 10
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local offset = 40
  local notchWidth = 1.5
  local notchDepth = 3
  local finalDepth = 8
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenWidth = finalDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  local cThickDiameter = 20
  local windowDepthFront = 12
  local windowDepthBack = 6
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.set<PERSON>ayer("K_AciliV30")
  G.setThickness(0)
  distance = 3*notchWidth+offset
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.sunkenFrame(point1,point2,finalDepth,vWideAngle,vWideDiameter)
  
  G.setLayer("H_Freze5mm_Ic_Kucuk")
  G.setThickness(-windowDepthFront)
  local xFit = X-2*(3*notchWidth+offset+sunkenWidth+30+c/2)
  local yFit = Y-2*(3*notchWidth+offset+sunkenWidth+30+c/2)
  for i = 0,1,1 do
    distanceY = 3*notchWidth+offset+sunkenWidth+i*yFit
    for j = 0,1,1 do
      distanceX = 3*notchWidth+offset+sunkenWidth+j*xFit
      point1 = {distanceX,distanceY}
      point2 = {distanceX+30,distanceY+30}
      point3 = {distanceX+30+c,distanceY}
      point4 = {distanceX+60+c,distanceY+30}
      point5 = {distanceX,distanceY+30+c}
      point6 = {distanceX+30,distanceY+60+c}
      point7 = {distanceX+30+c,distanceY+30+c}
      point8 = {distanceX+60+c,distanceY+60+c}
      G.rectangle(point1,point2) 
      G.rectangle(point3,point4) 
      G.rectangle(point5,point6) 
      G.rectangle(point7,point8) 
    end
  end
  
  G.setLayer("H_Freze5mm_Ic_Orta")
  G.setThickness(-windowDepthFront)
  distance = 3*notchWidth+offset+sunkenWidth
  point1 = {distance+60+2*c,distance}
  point2 = {X-(distance+60+2*c),distance+30}
  G.rectangle(point1,point2)

  point1 = {distance+60+2*c,distance+30+c}
  point2 = {X-(distance+60+2*c),distance+60+c}
  G.rectangle(point1,point2) 
  
  point1 = {distance+60+2*c,Y-distance}
  point2 = {X-(distance+60+2*c),Y-(distance+30)}
  G.rectangle(point1,point2)

  point1 = {distance+60+2*c,Y-(distance+30+c)}
  point2 = {X-(distance+60+2*c),Y-(distance+60+c)}
  G.rectangle(point1,point2)
  
  point1 = {distance,distance+60+2*c}
  point2 = {distance+30,Y-(distance+60+2*c)}
  G.rectangle(point1,point2)
  
  point1 = {distance+30+c,distance+60+2*c}
  point2 = {distance+60+c,Y-(distance+60+2*c)}
  G.rectangle(point1,point2)
  
  point1 = {X-distance,distance+60+2*c}
  point2 = {X-(distance+30),Y-(distance+60+2*c)}
  G.rectangle(point1,point2)
  
  point1 = {X-(distance+30+c),distance+60+2*c}
  point2 = {X-(distance+60+c),Y-(distance+60+2*c)}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze5mm_Ic_Buyuk")
  G.setThickness(-windowDepthFront)
  point1 = {distance+60+2*c,distance+60+2*c}
  point2 = {X-(distance+60+2*c),Y-(distance+60+2*c)}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  distance = 3*notchWidth+offset+sunkenWidth
  for i = 1, 4, 1 do
    if i<3 then
      distanceX = distance+i*30+((2*i)-1)*(c/2)
    else
      distanceX = distance+xFit-c/2+(i-3)*(30+c)
    end
    point1 = {distanceX,distance}
    point2 = {distanceX,Y-distance}
    G.line(point1,point2)
  end
  
  for i = 1, 4, 1 do
    if i<3 then
      distanceY = distance+i*30+((2*i)-1)*(c/2)
    else
      distanceY = distance+yFit-c/2+(i-3)*(30+c)
    end
    point1 = {distance,distanceY}
    point2 = {X-distance,distanceY}
    G.line(point1,point2)
  end
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  return true
end

require "ADekoDebugMode"
-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xLimit = 200
  yLimit = 200
  xMin = 140
  yMin = 140
  
  a = 40
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2
  local notchWidth = 4
  local sunkenDepth = 2
  local sunkenDepth1 = 4
  local finalDepth = 8
  local cMidDiameter = 10                          
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth2 = finalDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local overlap = 10
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  G.setLayer("Cep_Acma")

  G.setThickness(-finalDepth)
  point1 = {-overlap/2,-overlap/2}
  point2 = {a,Y+overlap/2}
  G.rectangle(point1,point2)
  
  point1 = {a-overlap,Y-a}
  point2 = {X-a+overlap,Y+overlap/2}
  G.rectangle(point1,point2)
  
  point1 = {X-a,-overlap/2}
  point2 = {X+overlap/2,Y+overlap/2}
  G.rectangle(point1,point2)
  
  point1 = {a-overlap,-overlap/2}
  point2 = {X-a+overlap,a}
  G.rectangle(point1,point2)
  
  G.setThickness(-(sunkenDepth+notchDepth))
  distance = a+1.5*notchWidth+2*sunkenWidth+sunkenWidth1+sunkenWidth2
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-(finalDepth-notchDepth))
  distance = a+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-sunkenDepth)
  distance = a+notchWidth+2*sunkenWidth+sunkenWidth1+sunkenWidth2
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV45")
  G.setThickness(-(finalDepth-notchDepth))
  distance = a+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setThickness(-(finalDepth-notchDepth-sunkenDepth))
  distance = a+notchWidth+sunkenWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  corner1,corner2 = G.sunkenFrame(point1,point2,sunkenDepth1,vMidAngle,vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setThickness(0)
  distance = a+notchWidth+sunkenWidth+sunkenWidth1+sunkenWidth2
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  corner3,corner4 = G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
  corner3[3] = 0
  corner4[3] = 0
  
  G.setLayer("K_Freze5mm")
  --Corner cleaning
  G.setThickness(0)
  G.cleanCorners(corner3,corner4,sunkenDepth,cThinDiameter)
  
  --distance = a+1.5*notchWidth+2*sunkenWidth+sunkenWidth1+sunkenWidth2
  --point1 = {distance,distance}
  --point2 = {X-distance,Y-distance}
  --G.cleanCorners(point1,point2,sunkenDepth+notchDepth,cThinDiameter)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = a+1.5*notchWidth+2*sunkenWidth+sunkenWidth1+sunkenWidth2
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  
  return true
end

require "ADekoDebugMode"
-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.make<PERSON>art<PERSON>hape()
  
  xMin = 140
  yMin = 140
  xLimit = 250
  yLimit = 250
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  local notchDepth = 2
  local finalDepth = 8
  local cMidDiameter = 10
  local bulge = G.bulge({X-a*1.5, Y-2.5*a, 0, 0}, {X/2, Y-a}, {a*1.5, Y-2.5*a})
  local point = {{X-a*1.5, Y-2.5*a, 0, bulge},{a*1.5, Y-2.5*a, 0, 0},{a*1.5, a},{X-a*1.5, a},{X-a*1.5, Y-2.5*a, 0, bulge}}
  
  G.setLayer("K_Desen")
  G.setThickness(-finalDepth)
  G.rectangle ({0,0},{X,Y})
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-finalDepth)
  G.polylineimp(point)
  G.polylineimp(G.offSet(point, -cMidDiameter/2))
  
  return true
end

------------------------------------------------
require "ADekoDebugMode"
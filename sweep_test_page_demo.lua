-- Sweep Operations Test Page Demo Script
-- This script demonstrates various sweep operations for testing

-- System variables
X = 300
Y = 200
materialThickness = 20

function modelMain()
    print("=== Sweep Operations Test Page Demo ===")
    print("This script creates comprehensive geometry for testing all sweep operations")

    -- Initialize AdekoLib
    G = ADekoLib

    -- Create the main door panel (PANEL layer)
    print("1. Creating main door panel...")
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePart<PERSON>hape()  -- This creates the PANEL layer automatically

    -- Test Section 1: Basic Cylindrical Tools
    print("2. Creating basic cylindrical tool operations...")

    -- Large endmill for roughing
    G.setLayer("12MM")
    G.setThickness(-5)
    G.rectangle({20, 20}, {120, 80})

    -- Medium endmill for general cutting
    G.setLayer("8MM")
    G.setThickness(-4)
    G.rectangle({140, 20}, {220, 60})
    <PERSON><PERSON>circle({180, 100}, 15)

    -- Small endmill for detail work
    G<PERSON>setLayer("6MM")
    G.setThickness(-3)
    <PERSON><PERSON>circle({50, 120}, 8)
    <PERSON><PERSON>circle({80, 120}, 8)
    <PERSON><PERSON>circle({110, 120}, 8)

    -- Precision endmill
    G.setLayer("4MM")
    G.setThickness(-2)
    G.line({20, 160}, {280, 160})

    -- Test Section 2: V-Bit Tools
    print("3. Creating V-bit tool operations...")

    -- V120 decorative edge
    G.setLayer("V120")
    G.setThickness(-2)
    G.polyline({10, 10}, {290, 10}, {290, 190}, {10, 190}, {10, 10})

    -- V90 chamfer
    G.setLayer("V90")
    G.setThickness(-1)
    G.rectangle({160, 80}, {220, 120})

    -- Test Section 3: Ballnose Tools
    print("4. Creating ballnose tool operations...")

    -- Large ballnose for 3D contouring
    G.setLayer("BALLNOSE8")
    G.setThickness(-3)
    G.circle({70, 70}, 25)

    -- Small ballnose for detail work
    G.setLayer("BALLNOSE4")
    G.setThickness(-2)
    G.circle({230, 70}, 12)

    -- Test Section 4: Complex Geometry
    print("5. Creating complex geometry for advanced testing...")

    -- Multi-step pocket
    G.setLayer("10MM")
    G.setThickness(-6)
    G.rectangle({200, 120}, {280, 180})

    G.setLayer("6MM")
    G.setThickness(-4)
    G.rectangle({210, 130}, {270, 170})

    G.setLayer("3MM")
    G.setThickness(-2)
    G.rectangle({220, 140}, {260, 160})

    -- Curved toolpath simulation
    G.setLayer("5MM")
    G.setThickness(-3)
    -- Create a simple curved path using multiple line segments
    G.line({120, 150}, {140, 160})
    G.line({140, 160}, {160, 165})
    G.line({160, 165}, {180, 160})
    G.line({180, 160}, {200, 150})

    -- Test Section 5: Performance Test Geometry
    print("6. Creating performance test patterns...")

    -- Grid of small holes for stress testing
    G.setLayer("3MM")
    G.setThickness(-1)
    for row = 1, 3 do
        for col = 1, 5 do
            local x = 40 + (col - 1) * 20
            local y = 40 + (row - 1) * 20
            G.circle({x, y}, 2)
        end
    end

    print("✅ All geometry created successfully!")
    return true
end

-- Load AdekoDebugMode to execute the script
require "ADekoDebugMode"

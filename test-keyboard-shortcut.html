<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keyboard Shortcut Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .test-area {
            border: 1px solid #444;
            padding: 20px;
            margin: 20px 0;
            background: #2d2d2d;
        }
        .contenteditable-test {
            border: 1px solid #666;
            padding: 10px;
            min-height: 100px;
            background: #1e1e1e;
            color: #fff;
            font-family: 'Courier New', monospace;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <h1>Keyboard Shortcut Test</h1>
    
    <div class="test-area">
        <h2>Test 1: Regular Input Field</h2>
        <input type="text" placeholder="Type here and try Ctrl+S" style="width: 100%; padding: 10px;">
    </div>
    
    <div class="test-area">
        <h2>Test 2: ContentEditable Div (Monaco Editor simulation)</h2>
        <div class="contenteditable-test" contenteditable="true">
            Type here and try Ctrl+S
            This simulates Monaco Editor's contentEditable behavior
        </div>
    </div>
    
    <div class="test-area">
        <h2>Test 3: Regular Div (should work)</h2>
        <div style="padding: 10px; border: 1px solid #666;">
            Click here and try Ctrl+S
        </div>
    </div>
    
    <div class="test-area">
        <h2>Event Log</h2>
        <div class="log" id="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Simulate the keyboard shortcut logic from useKeyboardShortcuts.ts
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function matchesShortcut(event, shortcut) {
            return (
                event.key.toLowerCase() === shortcut.key.toLowerCase() &&
                !!event.ctrlKey === !!shortcut.ctrlKey &&
                !!event.shiftKey === !!shortcut.shiftKey &&
                !!event.altKey === !!shortcut.altKey &&
                !!event.metaKey === !!shortcut.metaKey
            );
        }

        function handleKeyDown(event) {
            log(`Key pressed: ${event.key}, Ctrl: ${event.ctrlKey}, Target: ${event.target.tagName}, ContentEditable: ${event.target.contentEditable}`);
            
            // Don't handle shortcuts when typing in input fields (except for global shortcuts and important editor shortcuts)
            const target = event.target;
            if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
                // Allow global shortcuts and important editor shortcuts in input fields
                const globalShortcuts = ['F1', 'F2', 'F5', 'F7', 'F9'];
                const allowedInEditor = ['s', 'n', 'o', 'z', 'y', 'f', 'h']; // Save, New, Open, Undo, Redo, Find, Replace
                
                // Check if it's a global shortcut (function keys)
                if (globalShortcuts.includes(event.key)) {
                    log('✅ Global shortcut allowed');
                }
                // Check if it's an important editor shortcut with Ctrl
                else if (event.ctrlKey && allowedInEditor.includes(event.key.toLowerCase())) {
                    log('✅ Important editor shortcut allowed');
                }
                // Otherwise, don't handle the shortcut in input fields
                else {
                    log('❌ Shortcut blocked in input field');
                    return;
                }
            }

            // Test for Ctrl+S
            const saveShortcut = { key: 's', ctrlKey: true };
            if (matchesShortcut(event, saveShortcut)) {
                event.preventDefault();
                event.stopPropagation();
                log('🎉 CTRL+S DETECTED AND HANDLED!');
                alert('Ctrl+S shortcut triggered!');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Add event listener
        document.addEventListener('keydown', handleKeyDown, true);
        
        log('Keyboard shortcut test initialized. Try pressing Ctrl+S in different areas.');
    </script>
</body>
</html>

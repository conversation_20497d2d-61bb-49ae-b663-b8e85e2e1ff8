# Version Management

This project uses automatic version incrementing on git push to keep versions synchronized across all configuration files.

> **Note**: The version management system is now active and will automatically increment versions on every push.
> **Platform Support**: Works on Windows (batch/PowerShell), Linux, and macOS (bash).

## How It Works

### Automatic Version Increment (Git Hook)

Every time you run `git push`, a pre-push hook automatically:

1. **Increments the patch version** (e.g., 1.0.0 → 1.0.1)
2. **Updates all version files**:
   - `package.json`
   - `src-tauri/Cargo.toml`
   - `src-tauri/tauri.conf.json` (including productName and window title)
3. **Commits the version changes** with message: `chore: bump version to X.X.X`
4. **Continues with the push**

### Manual Version Management

You can also manually manage versions using npm scripts:

```bash
# Show current versions
npm run version

# Increment patch version (1.0.0 → 1.0.1)
npm run version:patch

# Increment minor version (1.0.0 → 1.1.0)
npm run version:minor

# Increment major version (1.0.0 → 2.0.0)
npm run version:major
```

Or use the scripts directly:

```bash
# Show current versions
node scripts/version.js current

# Increment versions
node scripts/version.js patch
node scripts/version.js minor
node scripts/version.js major
```

## Files Updated

The version management system keeps these files in sync:

1. **`package.json`** - Node.js package version
2. **`src-tauri/Cargo.toml`** - Rust crate version
3. **`src-tauri/tauri.conf.json`** - Tauri app version, product name, and window title

## Version Format

This project follows [Semantic Versioning](https://semver.org/):

- **MAJOR** version: incompatible API changes
- **MINOR** version: backwards-compatible functionality additions
- **PATCH** version: backwards-compatible bug fixes

## Disabling Auto-Increment

If you need to push without incrementing the version, you can:

1. **Temporarily rename the hook**:
   ```bash
   mv .git/hooks/pre-push .git/hooks/pre-push.disabled
   git push
   mv .git/hooks/pre-push.disabled .git/hooks/pre-push
   ```

2. **Use `--no-verify` flag** (skips all hooks):
   ```bash
   git push --no-verify
   ```

## Troubleshooting

### Hook Not Running

If the pre-push hook isn't running:

1. Check if the hook file exists: `.git/hooks/pre-push`
2. Ensure it's executable (on Windows, check file permissions)
3. Verify Node.js is available in your PATH

### Version Sync Issues

If versions get out of sync, run:

```bash
npm run version
```

This will show current versions and highlight any discrepancies.

### Manual Sync

To manually sync all versions to match package.json:

```bash
node scripts/increment-version.js patch
```

This will read the current version from package.json and update all other files to match.

## Development Workflow

1. Make your changes
2. Commit your changes: `git commit -m "your message"`
3. Push: `git push` (version auto-increments)
4. The push includes both your changes and the version bump

## Configuration

The version increment script is located at `scripts/increment-version.js` and can be customized if needed.

The pre-push hook is at `.git/hooks/pre-push` and can be modified to change the auto-increment behavior.

-- <PERSON><PERSON>oCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  r = 1.5
  bulge = math.tan(math.pi/8)
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.make<PERSON><PERSON><PERSON>({0,r}, {0,Y-r,0,-bulge}, {r,Y}, {X-r,Y,0,-bulge}, {X,Y-r}, {X,r,0,-bulge}, {X-r,0}, {r,0,0,-bulge}, {0,r})
  
  a = 55        -- offset from edges
  b = 10        -- gap
	
  G = ADekoLib
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local firstDepth = 1
  local finalDepth = 6
  
  G.set<PERSON>ayer("K_V135")
  G.setThickness(-firstDepth)
  G.rectangle({a, a}, {X-a, Y-a})

  G.setThickness(0)
  G.sunken<PERSON>({a+b, a+b}, {X-a-b, Y-a-b},finalDepth,135,55)
  
  -- model parameters
  G.showPar({0, Y/2}, {a, Y/2}, "a")
  G.showPar({X-a-b, Y/2}, {X-a, Y/2}, "b")
  G.showPar({a+b, a+b}, {X-a-b, Y-a-b}, "SunkenFrame V135")
  
  return true
end

require "ADekoDebugMode"
# Sweep Operations Test Suite

## Overview

The Sweep Operations Test Suite provides comprehensive testing capabilities for the CAD/CAM application's sweep operation functionality. This suite includes both integrated and standalone testing tools to validate OpenCascade.js sweep operations, boolean geometry operations, and 3D visualization.

## Components

### 1. Integrated Test Page (`SweepOperationsTestPage.vue`)

**Location**: `src/components/SweepOperationsTestPage.vue`

**Access**: Available through the main application sidebar (🔧 Sweep Test tab)

**Features**:
- Real-time 3D visualization with Three.js
- OCJS worker integration
- Console output monitoring
- Test result tracking
- Comprehensive test categories

**Test Categories**:
- **Basic Sweep Tests**: Door body creation, tool BRep generation, simple sweep operations
- **Advanced Sweep Tests**: Multi-tool operations, complex geometry, boolean operations
- **Performance Tests**: Large model testing, stress testing

### 2. Standalone Test Page (`test_sweep_operations.html`)

**Location**: `test_sweep_operations.html`

**Access**: Open directly in browser for independent testing

**Features**:
- Self-contained testing environment
- Visual test progress tracking
- Performance metrics
- Simulated test execution
- Modern responsive UI

### 3. Demo <PERSON>t (`sweep_test_page_demo.lua`)

**Location**: `sweep_test_page_demo.lua`

**Purpose**: Comprehensive Lua script demonstrating all sweep operation types

**Geometry Created**:
- Main door panel (300x200x20mm)
- Cylindrical tools: 12MM, 8MM, 6MM, 4MM, 3MM
- V-bit tools: V120, V90
- Ballnose tools: BALLNOSE8, BALLNOSE4
- Complex multi-step operations
- Performance test patterns

## Test Definitions

### Basic Operations

#### Door Body Creation
- **Purpose**: Test basic door panel generation from PANEL layer
- **Expected Result**: 3D door body created successfully
- **Validation**: Check OCJS worker logs for door body creation

#### Tool BRep Generation
- **Purpose**: Test 3D tool geometry creation for all tool types
- **Expected Result**: BRep geometries generated for each tool
- **Validation**: Verify tool shapes in 3D visualization

#### Simple Sweep Operation
- **Purpose**: Basic material removal with single tool
- **Expected Result**: Boolean subtraction operation completed
- **Validation**: 3D model shows material removal

### Advanced Operations

#### Multi-Tool Operations
- **Purpose**: Test multiple tools with different operations
- **Tools**: 8MM endmill, 6MM endmill, 4MM endmill
- **Operations**: Subtract, union combinations
- **Expected Result**: Complex 3D model with multiple operations

#### Boolean Operations
- **Purpose**: Test union, subtract, and intersect operations
- **Expected Result**: Proper boolean geometry operations
- **Validation**: Check operation results in 3D visualization

#### Complex Geometry
- **Purpose**: Advanced shapes with curves and fillets
- **Features**: V-bit decorative edges, ballnose contouring
- **Expected Result**: Complex 3D geometry with smooth curves

### Performance Tests

#### Large Model Test
- **Purpose**: Performance testing with complex door model
- **Scale**: 400x300x25mm door with 20+ operations
- **Expected Result**: Successful processing within reasonable time
- **Metrics**: Processing time, memory usage

#### Stress Test
- **Purpose**: Maximum complexity testing
- **Scale**: 500x400x30mm door with 50+ operations
- **Tools**: All tool types (12 different tools)
- **Expected Result**: System handles maximum complexity

## Usage Instructions

### Using the Integrated Test Page

1. **Access**: Open the main application and click the 🔧 Sweep Test tab in the sidebar
2. **Select Test**: Choose from Basic, Advanced, or Performance test categories
3. **Run Test**: Click individual test buttons or "Run All Tests"
4. **Monitor**: Watch console output and 3D visualization
5. **Results**: Check test status indicators and completion messages

### Using the Standalone Test Page

1. **Open**: Navigate to `test_sweep_operations.html` in your browser
2. **Select**: Choose from the available test categories
3. **Execute**: Run individual tests or the full suite
4. **Monitor**: Watch progress indicators and console output
5. **Review**: Check statistics and test results

### Using the Demo Script

1. **Load**: Open `sweep_test_page_demo.lua` in the main application
2. **Execute**: Run the script to generate comprehensive test geometry
3. **Visualize**: Switch to 3D visualization to see results
4. **Test**: Use the generated geometry for sweep operation testing

## Expected Results

### Successful Test Execution

- ✅ OCJS worker initializes without errors
- ✅ Door body created from PANEL layer geometry
- ✅ Tool BReps generated for all tool types
- ✅ Boolean operations complete successfully
- ✅ 3D visualization shows material removal
- ✅ GLB export functionality works
- ✅ Performance metrics within acceptable ranges

### Console Output Examples

```
[12:34:56] 🧪 Starting test: Door Body Creation
[12:34:56] ✅ OCJS Worker initialized successfully
[12:34:57] 🔧 Creating door body (200x150x18mm)
[12:34:58] ✅ Door body created: shape_12345
[12:34:59] ✅ Test completed: Door Body Creation (2.1s)
```

### Performance Benchmarks

- **Basic Operations**: < 5 seconds
- **Advanced Operations**: < 15 seconds
- **Large Model Test**: < 30 seconds
- **Stress Test**: < 60 seconds

## Troubleshooting

### Common Issues

#### OCJS Worker Initialization Fails
- **Cause**: Worker script not found or WASM loading issues
- **Solution**: Check worker path and OpenCascade.js installation
- **Debug**: Monitor browser console for detailed error messages

#### 3D Visualization Not Loading
- **Cause**: Three.js initialization issues or GLB loading problems
- **Solution**: Check Three.js version compatibility
- **Debug**: Verify GLB data is generated correctly

#### Test Timeouts
- **Cause**: Complex geometry or performance limitations
- **Solution**: Reduce geometry complexity or increase timeout values
- **Debug**: Monitor processing steps in console output

### Debug Mode

Enable detailed logging by setting debug flags:

```javascript
// In browser console
window.DEBUG_SWEEP_TESTS = true;
```

This will provide additional console output for troubleshooting.

## Integration with Main Application

The sweep operations test suite integrates seamlessly with the main CAD/CAM application:

- **Sidebar Navigation**: Accessible via the 🔧 icon in the main sidebar
- **OCJS Worker**: Uses the same worker as the main application
- **Three.js Integration**: Shares 3D visualization components
- **Lua Script Support**: Compatible with existing Lua execution system

## Future Enhancements

### Planned Features

1. **Automated Test Reporting**: Generate detailed test reports
2. **Performance Profiling**: Advanced performance metrics and analysis
3. **Test Comparison**: Compare results across different versions
4. **Custom Test Creation**: User-defined test scenarios
5. **Export Functionality**: Export test results and 3D models

### API Extensions

1. **Test Plugin System**: Allow custom test plugins
2. **Batch Testing**: Automated testing of multiple scenarios
3. **CI/CD Integration**: Continuous testing in development pipeline
4. **Performance Regression Testing**: Detect performance degradation

## Contributing

To add new tests or enhance the test suite:

1. **Test Definitions**: Add new test cases to `testDefinitions` object
2. **Lua Scripts**: Create corresponding Lua scripts for geometry generation
3. **Validation**: Implement proper result validation
4. **Documentation**: Update this documentation with new test details

## Support

For issues or questions regarding the sweep operations test suite:

1. Check the troubleshooting section above
2. Review console output for detailed error messages
3. Verify OCJS worker and Three.js integration
4. Test with simpler geometry to isolate issues

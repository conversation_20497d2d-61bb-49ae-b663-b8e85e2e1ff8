-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xLimit = 297
  yLimit = 297
  xMin = 140
  yMin = 140
  
  a = 70
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    
  local notchDepth = 2
  local sunkenDepth1 = 4
  local cThinDiameter = 6

  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("Cep_Acma")
  G.setThickness(-(sunkenDepth1+notchDepth))
  
  if Y>yLimit and X>xLimit then
    distance = a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_AciliV45")
  G.setThickness(-sunkenDepth1)
  
  point1 = {0,0}
  point2 = {X,Y}
  G.rectangle(point1,point2)
  
  point1 = {a,0}
  point2 = {a,Y}
  G.line(point1,point2)  
  
  point1 = {X-a,0}
  point2 = {X-a,Y}
  G.line(point1,point2)
  
  if Y>yLimit and X>xLimit then
    point1 = {a,a}
    point2 = {X-a,a}
    G.line(point1,point2)
  
    point1 = {a,Y-a}
    point2 = {X-a,Y-a}
    G.line(point1,point2)
  end
  
  G.setLayer("K_Freze6mm")
  G.setThickness(0)
  
  if Y>yLimit and X>xLimit then
    distance = a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.cleanCorners(point1,point2,notchDepth+sunkenDepth1,cThinDiameter)
  end
  
  return true
end

require "ADekoDebugMode"
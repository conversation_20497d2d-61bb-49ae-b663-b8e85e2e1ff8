-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  a = 0
  b = 0
  c = 10
  y = 10 -- yakla<PERSON>ık
  z = -5
  d = 1
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("POCKET")
  G.setThickness(z)
  if (d == 1) then 
    local n = math.floor((Y-2*b+c)/(y+c))
    local mX = X-2*a
    local mY = (Y-2*b-(n-1)*c)/(n)
    for i=0, n-1, 1
    do
      local rpX = a+mX/2
      local rpY = b+(i*mY)+(i*c)+mY/2;
      <PERSON><PERSON>set<PERSON>ayer("POCKET")
      G.rectangle({rpX-mX/2, rpY-mY/2}, {rpX+mX/2, rpY+mY/2})
    end
  else
    local n = math.floor((X-2*a+c)/(y+c))
    local mY = Y-2*b
    local mX = (X-2*a-(n-1)*c)/(n)
    for i=0, n-1, 1
    do
      local rpY = b+mY/2
      local rpX = a+(i*mX)+(i*c)+mX/2;
      G.setLayer("POCKET")
      G.rectangle({rpX-mX/2, rpY-mY/2}, {rpX+mX/2, rpY+mY/2})
    end
  end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"

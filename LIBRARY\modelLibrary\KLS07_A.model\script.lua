-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xLimit = 300
  yLimit = 300
  xMin = 140
  yMin = 140
  
  a = 75
	
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local cThinDiameter = 5
  local vNarrowAngle = 60
  local vNarrowDiameter = 30
  local sunkenWidth = finalDepth*math.tan((math.pi*vNarrowAngle/180)/2.0)
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end

  G.setLayer("K_AciliV60")
  G.setThickness(-finalDepth)
  G.line({a+sunkenWidth, 0}, {a+sunkenWidth, Y})
  G.line({X-a-sunkenWidth, 0}, {X-a-sunkenWidth, Y})
  
  if X>xLimit and Y>yLimit then
    G.setLayer("Cep_Acma")
    G.setThickness(-finalDepth)
    G.rectangle({a+sunkenWidth,a+sunkenWidth},{X-a-sunkenWidth,Y-a-sunkenWidth})
  
    G.setLayer("K_Freze5mm")
    G.setThickness(0)
    G.cleanCorners({a+sunkenWidth,a+sunkenWidth},{X-a-sunkenWidth,Y-a-sunkenWidth},finalDepth,cThinDiameter)
  end
  
  return true
end

require "ADekoDebugMode"
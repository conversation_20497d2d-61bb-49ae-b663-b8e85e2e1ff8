-- Visible Sweep Operation Test
-- Creates geometry with better proportions to see the sweep results clearly

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions - smaller and thicker for better visibility
AdekoLib.setPanelSize(100, 100, 30)  -- 100x100x30mm (thicker door)

print("=== Visible Sweep Operation Test ===")

-- Create the door panel (base geometry)
print("Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door", 0, 0, 100, 100)

-- Create LARGE cuts that will be clearly visible
print("Creating large visible cuts...")

-- Large rectangular pocket (20mm endmill, 15mm deep)
AdekoLib.layer("20MM")
AdekoLib.rect("big_pocket", 20, 20, 60, 30)  -- 40x10mm pocket

-- Large circular hole (15mm endmill, 10mm deep)  
AdekoLib.layer("15MM")
AdekoLib.circle("big_hole", 50, 70, 15)  -- 30mm diameter hole

-- Deep groove (10mm endmill, 20mm deep)
AdekoLib.layer("10MM") 
AdekoLib.rect("deep_groove", 10, 50, 90, 10)  -- Long groove across door

print("Geometry created for maximum visibility!")
print("- Door panel: 100x100x30mm (thick door)")
print("- Large pocket: 40x10mm with 20mm endmill")
print("- Large hole: 30mm diameter with 15mm endmill")
print("- Deep groove: 80x10mm with 10mm endmill")

-- Finish the model
AdekoLib.finish()

print("\n=== Visibility Test Instructions ===")
print("1. Load this script in the application")
print("2. Run the script")
print("3. Switch to 3D visualization")
print("4. You should clearly see:")
print("   - A thick door panel (30mm thick)")
print("   - Large rectangular pocket cut into the door")
print("   - Large circular hole")
print("   - Deep groove across the door")
print("5. Use mouse to rotate and zoom the 3D view")

print("\nExpected Results:")
print("- Thick door body should be clearly visible")
print("- Large cuts should be obvious material removal")
print("- No red wireframe issues")
print("- Smooth 3D geometry with proper boolean operations")

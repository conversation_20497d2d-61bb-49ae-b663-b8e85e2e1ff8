-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  r = 2
  bulge = math.tan(math.pi/8)
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  --G.makePartShape({0,r}, {0,Y-r,0,-bulge}, {r,Y}, {X-r,Y,0,-bulge}, {X,Y-r}, {X,r,0,-bulge}, {X-r,0}, {r,0,0,-bulge}, {0,r})
  G.makePartShape()
  
  a = 65
  c = 18
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local blockNoX = 1
  local blockNoY = 1
  local stepX = (blockNoX-1)		--Number of laths on X axis
  local stepY = (blockNoY-1)		--Number of laths on Y axis
  local innerX = X-2*a-stepX*c		--Width of the inner rectangle on the x axis
  local innerY = Y-2*a-stepY*c		--Length of the inner rectangle on the y axis
  local correctedblockX = innerX / blockNoX
  local correctedblockY = innerY / blockNoY
  local windowDepthBack = 7
  
  if X<140 or Y<140 then
    print("Part dimension too small")
    return true
  end
  
  for i=0,stepY do
    G.setLayer("H_Radus_Ic")		--Seperation of the blocks                            
    G.setThickness(-materialThickness)
    local y = a+(i)*c+(i+0.5)*(correctedblockY)
    for j=0,stepX do
      G.setLayer("H_Radus_Ic")		--Seperation of the blocks 
      G.setThickness(-materialThickness)
      local x = a+(j)*c+(j+0.5)*(correctedblockX)
      pc1 = {x-(c/(math.sqrt(2))),y}
      pc2 = {x,y+(c/(math.sqrt(2)))}
      pc3 = {x+(c/(math.sqrt(2))),y}
      pc4 = {x,y-(c/(math.sqrt(2)))}
      p1x = {x-(correctedblockX)/2+(c/(math.sqrt(2))),y-(correctedblockY)/2}
      p1y = {x-(correctedblockX)/2,y-(correctedblockY)/2+(c/(math.sqrt(2)))}
      p2x = {x-(correctedblockX)/2+(c/(math.sqrt(2))),y+(correctedblockY)/2}
      p2y = {x-(correctedblockX)/2,y+(correctedblockY)/2-(c/(math.sqrt(2)))}
      p3x = {x+(correctedblockX)/2-(c/(math.sqrt(2))),y+(correctedblockY)/2}
      p3y = {x+(correctedblockX)/2,y+(correctedblockY)/2-(c/(math.sqrt(2)))}
      p4x = {x+(correctedblockX)/2-(c/(math.sqrt(2))),y-(correctedblockY)/2}
      p4y = {x+(correctedblockX)/2,y-(correctedblockY)/2+(c/(math.sqrt(2)))}
      G.polyline(pc1,p1y,p2y,pc1)
      G.polyline(pc2,p2x,p3x,pc2)
      G.polyline(pc3,p3y,p4y,pc3)
      G.polyline(pc4,p4x,p1x,pc4)
    end
  end
  
  G.setFace("bottom")
  
--  G.setLayer("K_Freze20mm_SF")		--Clearing procedures of the backside
--  for i=0,stepY do
--    G.setThickness(-windowDepthBack)
--    local y = a+(i)*c+(i+0.5)*(correctedblockY)
--    for j=0,stepX do
--      G.setThickness(-windowDepthBack)
--      local x = a+(j)*c+(j+0.5)*(correctedblockX)
--      point1 = {x-(correctedblockX)/2,y-(correctedblockY)/2}
--      point2 = {x-(correctedblockX)/2,y+(correctedblockY)/2}
--      point3 = {x+(correctedblockX)/2,y+(correctedblockY)/2}
--      point4 = {x+(correctedblockX)/2,y-(correctedblockY)/2}
--      G.line(point1,point3)
--      G.line(point2,point4)
--    end
--  end
  
--  for i = 0, stepX-1 do
--    G.setThickness(-windowDepthBack)
--    local y = a
--    local x = a+(i+1)*correctedblockX+i*c+c/2
--    point1 = {x,y}
--    point2 = {x,Y-y}
--    G.line(point1,point2,0)
--  end
  
--  for j = 0, stepY-1 do
--    G.setThickness(-windowDepthBack)
--    local x = a
--    local y = a+(j+1)*correctedblockY+j*c+c/2
--    point1 = {x,y}
--    point2 = {X-x,y}
--    G.line(point1,point2,0)
--  end
  
  G.setLayer("Cep_Ac")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-10, a-10}, {X-a+10, Y-a+10})
  
--  G.setLayer("K_Freze20mm_SF")
--  G.setThickness(-windowDepthBack)
--  G.rectangle({a, a}, {X-a, Y-a})
  
  return true
end

require "ADekoDebugMode"
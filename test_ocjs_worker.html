<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCJS Worker Sweep Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OpenCascade.js Worker Sweep Operation Test</h1>
        <p>This page tests the OCJS worker sweep functionality independently.</p>

        <div class="test-section">
            <h3>1. Worker Initialization</h3>
            <button onclick="initWorker()">Initialize OCJS Worker</button>
            <div id="init-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Door Body Creation</h3>
            <button onclick="testDoorBody()" disabled>Create Door Body</button>
            <div id="door-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. Tool BRep Creation</h3>
            <button onclick="testToolBRep()" disabled>Create Tool BRep</button>
            <div id="tool-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Multiple Tools BRep</h3>
            <button onclick="testAllToolBReps()" disabled>Create All Tool BReps</button>
            <div id="tools-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. Sweep Operation</h3>
            <button onclick="testSweepOperation()" disabled>Perform Sweep Operation</button>
            <div id="sweep-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. GLB Export</h3>
            <button onclick="testGLBExport()" disabled>Export to GLB</button>
            <div id="glb-result" class="result"></div>
        </div>
    </div>

    <script>
        let worker = null;
        let messageId = 0;
        let pendingMessages = new Map();
        let doorBodyShape = null;
        let toolShapes = [];

        // Sample tool definitions
        const testTools = [
            {
                id: 'test-8mm',
                name: '8mm Endmill',
                shape: 'cylindrical',
                diameter: 8,
                length: 50,
                material: 'carbide'
            },
            {
                id: 'test-6mm',
                name: '6mm Endmill', 
                shape: 'cylindrical',
                diameter: 6,
                length: 40,
                material: 'carbide'
            },
            {
                id: 'test-v90',
                name: '90° V-bit',
                shape: 'conical',
                diameter: 10,
                length: 30,
                tipDiameter: 0.2,
                angle: 90
            },
            {
                id: 'test-ball4',
                name: '4mm Ballnose',
                shape: 'ballnose',
                diameter: 4,
                length: 35,
                ballRadius: 2
            }
        ];

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        function sendMessage(type, data) {
            return new Promise((resolve, reject) => {
                if (!worker) {
                    reject(new Error('Worker not initialized'));
                    return;
                }

                const id = `msg_${++messageId}`;
                pendingMessages.set(id, { resolve, reject });

                worker.postMessage({ id, type, data });
            });
        }

        async function initWorker() {
            try {
                log('init-result', 'Initializing OCJS Worker...', 'loading');
                
                // This would need to be adapted to your actual worker path
                worker = new Worker('/src/workers/ocjsWorker.ts', { type: 'module' });
                
                worker.onmessage = (event) => {
                    const { id, type, data, error } = event.data;
                    const pending = pendingMessages.get(id);
                    
                    if (pending) {
                        pendingMessages.delete(id);
                        if (type === 'success') {
                            pending.resolve(data);
                        } else {
                            pending.reject(new Error(error || 'Unknown worker error'));
                        }
                    }
                };

                worker.onerror = (error) => {
                    log('init-result', `Worker error: ${error.message}`, 'error');
                };

                // Enable subsequent tests
                document.querySelectorAll('button').forEach(btn => {
                    if (btn.textContent !== 'Initialize OCJS Worker') {
                        btn.disabled = false;
                    }
                });

                log('init-result', 'OCJS Worker initialized successfully!', 'success');
            } catch (error) {
                log('init-result', `Failed to initialize worker: ${error.message}`, 'error');
            }
        }

        async function testDoorBody() {
            try {
                log('door-result', 'Creating door body...', 'loading');
                
                const params = {
                    width: 200,
                    height: 150, 
                    thickness: 18,
                    cornerRadius: 0
                };

                const result = await sendMessage('createDoorBody', params);
                doorBodyShape = result; // Store for later use
                
                log('door-result', `Door body created successfully!\nDimensions: ${params.width}x${params.height}x${params.thickness}mm`, 'success');
            } catch (error) {
                log('door-result', `Failed to create door body: ${error.message}`, 'error');
            }
        }

        async function testToolBRep() {
            try {
                log('tool-result', 'Creating tool BRep...', 'loading');
                
                const params = {
                    tool: testTools[0], // 8mm endmill
                    height: 50,
                    includeGLB: false
                };

                const result = await sendMessage('createToolBRep', params);
                
                log('tool-result', `Tool BRep created successfully!\nTool: ${params.tool.name}\nShape: ${result.toolShape}\nDiameter: ${result.diameter}mm`, 'success');
            } catch (error) {
                log('tool-result', `Failed to create tool BRep: ${error.message}`, 'error');
            }
        }

        async function testAllToolBReps() {
            try {
                log('tools-result', 'Creating all tool BReps...', 'loading');
                
                const params = {
                    tools: testTools,
                    height: 50,
                    includeGLB: false
                };

                const result = await sendMessage('createAllToolBReps', params);
                toolShapes = result.results; // Store for later use
                
                log('tools-result', `All tool BReps created!\nTotal tools: ${result.count}\nSuccessful: ${result.successCount}\nTools: ${testTools.map(t => t.name).join(', ')}`, 'success');
            } catch (error) {
                log('tools-result', `Failed to create tool BReps: ${error.message}`, 'error');
            }
        }

        async function testSweepOperation() {
            try {
                log('sweep-result', 'Performing sweep operation...', 'loading');
                
                if (!doorBodyShape) {
                    throw new Error('Door body not created yet. Run door body test first.');
                }

                if (toolShapes.length === 0) {
                    throw new Error('Tool shapes not created yet. Run tool BRep tests first.');
                }

                const params = {
                    doorBodyShape: doorBodyShape,
                    toolGeometries: toolShapes,
                    operation: 'subtract'
                };

                const result = await sendMessage('performSweepOperation', params);
                
                log('sweep-result', `Sweep operation completed successfully!\nOperation: ${params.operation}\nResult shape ID: ${result.shapeId}\nTools processed: ${toolShapes.length}`, 'success');
            } catch (error) {
                log('sweep-result', `Failed to perform sweep operation: ${error.message}`, 'error');
            }
        }

        async function testGLBExport() {
            try {
                log('glb-result', 'Exporting to GLB...', 'loading');
                
                const params = {
                    width: 200,
                    height: 150,
                    thickness: 18
                };

                const result = await sendMessage('createSimpleBoxGLB', params);
                
                // Create download link
                const blob = new Blob([result], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'sweep_test.glb';
                a.textContent = 'Download GLB';
                
                log('glb-result', `GLB export successful!\nFile size: ${result.byteLength} bytes\n`, 'success');
                document.getElementById('glb-result').appendChild(a);
            } catch (error) {
                log('glb-result', `Failed to export GLB: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

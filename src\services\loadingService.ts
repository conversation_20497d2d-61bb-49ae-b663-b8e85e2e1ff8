export interface LoadingStep {
  id: string
  name: string
  progress: number
  completed: boolean
}

export class LoadingService {
  private static instance: LoadingService
  private steps: LoadingStep[] = []
  private currentStepIndex = 0
  private progressBar: HTMLElement | null = null
  private statusElement: HTMLElement | null = null
  private loaderElement: HTMLElement | null = null

  private constructor() {
    this.initializeElements()
    this.setupSteps()
  }

  public static getInstance(): LoadingService {
    if (!LoadingService.instance) {
      LoadingService.instance = new LoadingService()
    }
    return LoadingService.instance
  }

  private initializeElements(): void {
    this.progressBar = document.getElementById('progress-bar')
    this.statusElement = document.getElementById('loader-status')
    this.loaderElement = document.getElementById('app-loader')
  }

  private setupSteps(): void {
    this.steps = [
      { id: 'init', name: 'Initializing application', progress: 0, completed: false },
      { id: 'themes', name: 'Loading themes', progress: 0, completed: false },
      { id: 'i18n', name: 'Setting up internationalization', progress: 0, completed: false },
      { id: 'settings', name: 'Loading user settings', progress: 0, completed: false },
      { id: 'monaco', name: 'Initializing Monaco Editor', progress: 0, completed: false },
      { id: 'editor', name: 'Setting up editor workspace', progress: 0, completed: false },
      { id: 'services', name: 'Starting background services', progress: 0, completed: false },
      { id: 'complete', name: 'Ready!', progress: 100, completed: false }
    ]
  }

  public startStep(stepId: string): void {
    const step = this.steps.find(s => s.id === stepId)
    if (!step) return

    this.currentStepIndex = this.steps.indexOf(step)
    this.updateStatus(`${step.name}...`)
    this.updateProgress()
  }

  public completeStep(stepId: string): void {
    const step = this.steps.find(s => s.id === stepId)
    if (!step) return

    step.completed = true
    step.progress = 100

    // Move to next step if available
    const nextStepIndex = this.steps.indexOf(step) + 1
    if (nextStepIndex < this.steps.length) {
      this.currentStepIndex = nextStepIndex
      const nextStep = this.steps[nextStepIndex]
      this.updateStatus(`${nextStep.name}...`)
    }

    this.updateProgress()

    // If this is the last step, hide the loader
    if (stepId === 'complete') {
      setTimeout(() => this.hideLoader(), 500)
    }
  }

  public updateStepProgress(stepId: string, progress: number): void {
    const step = this.steps.find(s => s.id === stepId)
    if (!step) return

    step.progress = Math.min(100, Math.max(0, progress))
    this.updateProgress()
  }

  private updateProgress(): void {
    if (!this.progressBar) return

    // Calculate overall progress
    const completedSteps = this.steps.filter(s => s.completed).length
    const currentStep = this.steps[this.currentStepIndex]
    const currentStepProgress = currentStep ? currentStep.progress : 0

    // Overall progress calculation
    const baseProgress = (completedSteps / this.steps.length) * 100
    const currentStepContribution = (currentStepProgress / this.steps.length)
    const totalProgress = Math.min(100, baseProgress + currentStepContribution)

    this.progressBar.style.width = `${totalProgress}%`
  }

  private updateStatus(message: string): void {
    if (!this.statusElement) return
    
    // Remove dots animation temporarily and add it back
    this.statusElement.innerHTML = message
    
    // Add dots animation if message doesn't end with "Ready!"
    if (!message.includes('Ready!')) {
      this.statusElement.innerHTML = message + '<span class="loader-dots"></span>'
    }
  }

  public hideLoader(): void {
    if (!this.loaderElement) return

    this.loaderElement.classList.add('hidden')
    
    // Remove the loader element after transition
    setTimeout(() => {
      if (this.loaderElement && this.loaderElement.parentNode) {
        this.loaderElement.parentNode.removeChild(this.loaderElement)
      }
    }, 500)
  }

  public showError(message: string): void {
    this.updateStatus(`Error: ${message}`)
    if (this.progressBar) {
      this.progressBar.style.background = 'linear-gradient(90deg, #ff4757, #ff6b7a)'
    }
  }

  // Utility method to simulate async operations with progress
  public async simulateAsyncOperation(
    stepId: string, 
    operation: () => Promise<void>, 
    duration: number = 1000
  ): Promise<void> {
    this.startStep(stepId)
    
    const startTime = Date.now()
    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(100, (elapsed / duration) * 100)
      this.updateStepProgress(stepId, progress)
    }, 50)

    try {
      await operation()
      clearInterval(progressInterval)
      this.completeStep(stepId)
    } catch (error) {
      clearInterval(progressInterval)
      this.showError(`Failed to ${stepId}`)
      throw error
    }
  }

  // Method to run multiple steps in sequence
  public async runSteps(steps: Array<{
    id: string,
    operation: () => Promise<void>,
    duration?: number
  }>): Promise<void> {
    for (const step of steps) {
      await this.simulateAsyncOperation(step.id, step.operation, step.duration)
    }
  }

  // Add a delay for better UX (prevents flash of loading screen)
  public async addMinimumLoadTime(operation: () => Promise<void>, minTime: number = 1000): Promise<void> {
    const startTime = Date.now()
    await operation()
    const elapsed = Date.now() - startTime

    if (elapsed < minTime) {
      await new Promise(resolve => setTimeout(resolve, minTime - elapsed))
    }
  }

  // Check if loader is currently visible
  public isVisible(): boolean {
    return this.loaderElement ? !this.loaderElement.classList.contains('hidden') : false
  }

  // Force hide loader (emergency method)
  public forceHide(): void {
    if (this.loaderElement) {
      this.loaderElement.style.display = 'none'
    }
  }
}

// Export singleton instance
export const loadingService = LoadingService.getInstance()

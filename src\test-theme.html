<!DOCTYPE html>
<html>
<head>
    <title>Theme Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .editor-container {
            width: 800px;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Monaco Editor Theme Test</h1>
    
    <div class="controls">
        <button onclick="setTheme('vs')">Light Theme</button>
        <button onclick="setTheme('vs-dark')">Dark Theme</button>
        <button onclick="setTheme('vibrant-dark')">Vibrant Dark</button>
        <button onclick="setTheme('vibrant-light')">Vibrant Light</button>
        <button onclick="setTheme('neon-dark')">Neon Dark</button>
    </div>
    
    <div id="editor" class="editor-container"></div>
    
    <script type="module">
        import * as monaco from 'https://cdn.skypack.dev/monaco-editor@0.45.0';
        
        // Sample Lua code
        const luaCode = `-- Sample Lua code with AdekoLib functions
function modelMain()
    -- Set up the drawing environment
    ADekoLib.setLayer("LMM0")
    ADekoLib.setThickness(18)
    ADekoLib.setFace(1)
    
    -- Draw some shapes
    local x, y = 100, 100
    ADekoLib.point(x, y)
    ADekoLib.line(x, y, x + 50, y + 50)
    
    -- Use some Lua built-ins
    for i = 1, 5 do
        print("Iteration: " .. i)
        local radius = i * 10
        ADekoLib.circle(x + i * 20, y, radius)
    end
    
    -- String operations
    local message = "Hello, World!"
    local length = string.len(message)
    print("Message length: " .. length)
    
    -- Math operations
    local angle = math.pi / 4
    local cos_val = math.cos(angle)
    
    return true
end`;

        // Define custom themes
        const vibrantDarkTheme = {
            base: 'vs-dark',
            inherit: true,
            rules: [
                { token: 'keyword', foreground: 'ff7b72', fontStyle: 'bold' },
                { token: 'string', foreground: 'a5d6ff' },
                { token: 'number', foreground: '79c0ff' },
                { token: 'comment', foreground: '8b949e', fontStyle: 'italic' },
                { token: 'function', foreground: 'd2a8ff', fontStyle: 'bold' },
                { token: 'identifier', foreground: 'e6edf3' }
            ],
            colors: {
                'editor.background': '#0d1117',
                'editor.foreground': '#e6edf3',
                'editor.lineHighlightBackground': '#161b22',
                'editor.selectionBackground': '#264f78',
                'editorLineNumber.foreground': '#7d8590',
                'editorCursor.foreground': '#79c0ff'
            }
        };
        
        // Register custom theme
        monaco.editor.defineTheme('vibrant-dark', vibrantDarkTheme);
        
        // Create editor
        const editor = monaco.editor.create(document.getElementById('editor'), {
            value: luaCode,
            language: 'lua',
            theme: 'vs-dark',
            fontSize: 16,
            fontFamily: 'Consolas, "Courier New", Monaco, monospace',
            lineHeight: 1.6,
            minimap: { enabled: true },
            wordWrap: 'on'
        });
        
        // Theme switching function
        window.setTheme = function(themeName) {
            try {
                monaco.editor.setTheme(themeName);
                console.log('Theme set to:', themeName);
            } catch (error) {
                console.error('Failed to set theme:', themeName, error);
                alert('Theme not available: ' + themeName);
            }
        };
        
        // Test theme availability
        console.log('Testing themes...');
        setTimeout(() => {
            window.setTheme('vibrant-dark');
        }, 1000);
    </script>
</body>
</html>

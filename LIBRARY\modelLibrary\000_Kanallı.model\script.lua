function modelMain()
  
	ktip=1 -- 1-tek cizgi/2-cift cizgi
  
	K1ken=1 -- 1-ust(en)/2-alt(en)/3-sag(boy)/4-sol(boy)
	K1bas=10 --kanal baslangic kenardan mesafe (taşma tarafi)
	K1bit=20 --kanal bitis kenardan mesafe (taşma tarafi)
	K1off=10 --kanal merkezinin yan kenara uzakligi
	K1der=10 --kanal derinligi
	K1gen=15 --kanal genisligi

	K2ken=3 -- 1-ust(en)/2-alt(en)/3-sag(boy)/4-sol(boy)
	K2bas=20 --kanal baslangic kenardan mesafe (taşma tarafi)
	K2bit=20 --kanal bitis kenardan mesafe (taşma tarafi)
	K2off=10 --kanal merkezinin yan kenara uzakligi
	K2der=10 --kanal derinligi
	K2gen=20 --kanal genisligi
	
	K3ken=2 -- 1-ust(en)/2-alt(en)/3-sag(boy)/4-sol(boy)
	K3bas=10 --kanal baslangic kenardan mesafe (taşma tarafi)
	K3bit=20 --kanal bitis kenardan mesafe (taşma tarafi)
	K3off=10 --kanal merkezinin yan kenara uzakligi
	K3der=10 --kanal derinligi
	K3gen=12 --kanal genisligi

  G=ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
      return false
  end


	function BBGroove (kken,bas,bit,off,der,gen)
		G.setLayer("GROOVE_" .. gen)
		G.setThickness(-der)
		if kken == 2 then
			if ktip == 2 then
			G.rectangle({bas, off-gen/2}, {X-bit, off+gen/2})
			else
			G.line({bas, off}, {X-bit, off})
			end
		elseif kken == 1 then
			if ktip == 2 then
			G.rectangle({bas, Y-off+gen/2}, {X-bit, Y-off-gen/2})
			else
			G.line({bas, Y-off}, {X-bit, Y-off})
			end
		elseif kken == 3 then
			if ktip == 2 then
			G.rectangle({off-gen/2, bas}, {off+gen/2, Y-bit})
			else
			G.line({off, bas}, {off, Y-bit})
			end
		elseif kken == 4 then
			if ktip == 2 then
			G.rectangle({X-off-gen/2, bas}, {X-off+gen/2, Y-bit})
			else
			G.line({X-off, bas}, {X-off, Y-bit})
			end
		end
	end
	
	if K1ken >0 then
		BBGroove (K1ken,K1bas,K1bit,K1off,K1der,K1gen)
  end
	if K2ken >0 then
		BBGroove (K2ken,K2bas,K2bit,K2off,K2der,K2gen)
  end
	if K3ken >0 then
		BBGroove (K3ken,K3bas,K3bit,K3off,K3der,K3gen)
	end

 return true
end

require "ADekoDebugMode"

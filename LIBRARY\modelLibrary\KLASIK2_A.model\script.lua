-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xMin = 250
  yMin = 250
  
  a = 55
  h = 25
  g = 145
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vMidAngle = 90
  local vMidDiameter = 40
  local finalDepth = 8
  local sunkenWidth = finalDepth * math.tan((math.pi*vMidAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.set<PERSON>ayer("K_AciliV45")		--Angled surface
  G.setThickness(0)
  local corner1,corner2 = G.sunkenFrame({a, a}, {X-a, Y-a}, finalDepth, vMidAngle, vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setThickness(-finalDepth)
  G.rectangle({0,0},{X,Y})
  
  G.setLayer("K_Freze10mm")		--Middle shape
  G.setThickness(-finalDepth)
  
  distance = a+sunkenWidth+h
  point1 = {distance-cMidDiameter/2, distance-cMidDiameter/2}
  point2 = {distance+g+cMidDiameter/2, Y-distance+cMidDiameter/2}
  point3 = {X-distance-g-cMidDiameter/2, distance-cMidDiameter/2}
  point4 = {X-distance+cMidDiameter/2, Y-distance+cMidDiameter/2}
  G.rectangle(point1, point2)
  G.rectangle(point3, point4)
  
  l = X-2*(a+sunkenWidth+h+g)
  k = math.floor((l-cMidDiameter)/(cMidDiameter/2))
  point5 = {distance+g+cMidDiameter/2,distance-cMidDiameter/2}
  point6 = {distance+g+cMidDiameter/2,Y-distance+cMidDiameter/2}
  
  if l>2*cMidDiameter then
    for i = 1, k ,1 do
      point5 = G.ptAdd(point5, {cMidDiameter/2,0})
      point6 = G.ptAdd(point6, {cMidDiameter/2,0})
      if point5[1]>=point3[1]-cMidDiameter/4 then
        break
      end
      G.line(point5,point6,0)
    end
  end
  
  G.setLayer("K_Freze10mm")		--Channel cleaning
  G.setThickness(-finalDepth)
  
  distance = a+sunkenWidth+cMidDiameter/2
  point1 = {distance,distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  k = math.floor((h-cMidDiameter)/(cMidDiameter/2))
  
  if h>cMidDiameter then
    for i = 1, k ,1 do
      point1 = G.ptAdd(point1, {cMidDiameter/2,cMidDiameter/2})
      point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
      if point2[1]<=point4[1] then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setLayer("K_Freze5mm")		--Corner cleaning
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,finalDepth,cThinDiameter)
  return true
end


require "ADekoDebugMode"

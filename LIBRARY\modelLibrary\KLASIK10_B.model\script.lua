-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()

  a = 75
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  G.setLayer("K_Freze5mm")
  G.setThickness(-finalDepth)
  G.line({a, 0}, {a, Y})
  G.line({X-a, 0}, {X-a, Y})
  
  G.setThickness(-windowDepthFront)
  G.rectangle({a,a},{X-a,Y-a})
  
  G.set<PERSON>ace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-10, a-10}, {X-a+10, Y-a+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a, a}, {X-a, Y-a})
  
  return true
end

require "ADekoDebugMode"
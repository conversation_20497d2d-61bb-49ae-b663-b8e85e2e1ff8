-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 50
  spacing = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
	
  finalDepth = 8
  
  if X < 150 or Y < 120 then
    print("Part dimension too small")
    return true
  end
  
  return Rustic_Shallow(X, Y)
end

------------------------------------------------
function Rustic_Shallow(X, Y)
    local horDist = X-2*a
    local verDist = Y-2*a
    local nLines = math.floor(horDist / spacing)
    local modifiedSpacing = horDist / nLines
    local i = 1
    G<PERSON>setLayer("K_Freze5mm")
    G.setThickness(-finalDepth)
    while i <= nLines-1 do
        local x = a + i * modifiedSpacing
        if (i % 2 == 0) then
            G.line(
                {x, a, 0, 0},
                {x, a + verDist, 0, 0}
            )
        else
            G.line(
                {x, a + verDist, 0, 0},
                {x, a, 0, 0}
            )
        end
        i = i + 1
    end
    G.setLayer("K_Freze10mm")
    G.setThickness(-finalDepth)
    G.rectangle({a, a}, {X-a, Y-a})
    return true
end

require "ADekoDebugMode"
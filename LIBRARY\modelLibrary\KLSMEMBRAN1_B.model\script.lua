-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xMin = 140
  yMin = 140
  
  a = 5
  g = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchWidth = 2.5
  local notchDepth = 1.25
  local sunkenDepth = 5
  local sunkenDepth2 = 7.5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-(sunkenDepth+notchDepth))
  distance = a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Ic")     
  G.setThickness(-(sunkenDepth+notchDepth))
  distance = a+sunkenWidth+g+sunkenWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV45")
  
  G.setThickness(-sunkenDepth)
  distance = a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setThickness(0)
  distance = a+sunkenWidth+g
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  corner1,corner2 = G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = a+sunkenWidth+g+sunkenWidth+notchWidth
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  
  return true
end

require "ADekoDebugMode"
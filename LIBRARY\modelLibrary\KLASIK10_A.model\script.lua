-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xLimit = 300
  yLimit = 300
  xMin = 140
  yMin = 140
  
  a = 75
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local cThinDiameter = 5
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_Freze5mm")
  G.setThickness(-finalDepth)
  G.line({a, 0}, {a, Y})
  G.line({X-a, 0}, {X-a, Y})
  
  if X>xLimit and Y>yLimit then
  
    <PERSON><PERSON>setLayer("Cep_Acma")
    G.setThickness(-finalDepth)
    G.rectangle({a,a},{X-a,Y-a})
  
    <PERSON><PERSON>setLayer("K_Freze5mm")
    G.setThickness(0)
    G.cleanCorners({a,a},{X-a,Y-a},finalDepth,cThinDiameter)
  
  end
  
  return true
end

require "ADekoDebugMode"
-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
	
  if X < 150 or Y < 120 then
    print("Part dimension too small")
    return true
  end
  
  local point1 = {X-a*1.5, Y-2.5*a, 0, 0}
  local point2 = {a*1.5, Y-2.5*a, 0, 0}
  local point3 = {a*1.5, a}
  local point4 = {X-a*1.5, a}
  local bulge = G.bulge(point1, {X/2, Y-a}, point2)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
  
  point1[4] = bulge
  points = {point1,point2,point3,point4,point1}
  
  <PERSON>.setLayer("H_Freze10mm_Ic")
  G.setThickness(-5)
  G.polylineimp(points)
  
  G.setThickness(-windowDepthFront)
  G.polylineimp(G.offSet(points,-5))
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,10))
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,-5))
  return true
end

require "ADekoDebugMode"
-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()

  xMin = 140
  yMin = 140
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2
  local sunkenDepth = 4
  local cThickDiameter = 20
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.set<PERSON>ayer("K_AciliV45")		--Angled surfaces
  G.setThickness(-sunkenDepth)
  distance1 = {0,0}
  distance2 = {X,Y}
  G.rectangle(distance1,distance2)
  
  G.setThickness(0)
  distance = sunkenWidth+a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  corner1, corner2 = G.sunkenFrame(point1,point2,2.5*sunkenDepth,vMidAngle,vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setLayer("K_Freze20mm")		--Notches and cleaning
  G.setThickness(-notchDepth)
  point1 = {sunkenWidth+a,sunkenWidth+a+cThickDiameter/2,0,0}
  point2 = ADekoLib.ptAdd(point1,{0,Y-2*(sunkenWidth+a)-cThickDiameter,0})
  point2[4] = -math.tan(math.pi/8)
  point3 = ADekoLib.ptAdd(point2,{cThickDiameter/2,cThickDiameter/2,0})
  point4 = ADekoLib.ptAdd(point3,{X-2*(sunkenWidth+a+cThickDiameter/2),0,0})
  point4[4] = -math.tan(math.pi/8)
  point5 = ADekoLib.ptAdd(point4,{cThickDiameter/2,-cThickDiameter/2,0})
  point6 = ADekoLib.ptAdd(point5,{0,-Y+2*(sunkenWidth+a+cThickDiameter/2),0})
  point6[4] = -math.tan(math.pi/8)
  point7 = ADekoLib.ptAdd(point6,{-cThickDiameter/2,-cThickDiameter/2,0})
  point8 = ADekoLib.ptAdd(point7,{-X+2*(sunkenWidth+a+cThickDiameter/2),0,0})
  point8[4] = -math.tan(math.pi/8)
  G.polyline(point1,point2,point3,point4,point5,point6,point7,point8,point1)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = 3.5*sunkenWidth+a
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  
  return true
end

require "ADekoDebugMode"
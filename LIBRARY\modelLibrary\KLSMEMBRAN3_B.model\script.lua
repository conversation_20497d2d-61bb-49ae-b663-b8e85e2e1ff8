-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xMin = 140
  yMin = 140
  
  a = 6
  g = 60
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchWidth = 2.5
  local notchDepth = 2
  local sunkenDepth = 4
  local cMidDiameter = 10
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("H_Freze10mm_Dis") 
  --Outermost notch
  G.setThickness(-2*notchDepth)
  distance = a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  --Second outer notch
  G.setThickness(-notchDepth)
  distance = a+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Ic")		--Other notches                              
  G.setThickness(-notchDepth)
  distance = a+notchWidth+g
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setThickness(-2*notchDepth)
  distance = a+2*notchWidth+g
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV45")		--Angled surface
  G.setThickness(-2*notchDepth)
  distance = a+3*notchWidth+g
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.sunkenFrame(point1,point2,sunkenDepth,vMidAngle,vMidDiameter)
  
  G.setLayer("H_Freze10mm_Ic")		--windows
  G.setThickness(-windowDepthFront)
  distance = a+3*notchWidth+g+sunkenWidth
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  return true
end

require "ADekoDebugMode"
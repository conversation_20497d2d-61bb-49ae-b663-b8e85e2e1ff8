-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()

  a = 70
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local vNarrowAngle = 60
  local vNarrowDiameter = 30
  local sunkenWidth = finalDepth*math.tan((math.pi*vNarrowAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X < 150 or Y < 120 then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_AciliV60")
  G.setThickness(-finalDepth)
  
  local point1 = {X-a-sunkenWidth, Y-a-sunkenWidth, 0, 0}
  local bulge = G.bulge(point1, {X/2, Y-a/2-sunkenWidth}, {a, Y-a-sunkenWidth})
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
  point1[4] = bulge
  local point2 = {a+sunkenWidth, Y-a-sunkenWidth, 0, 0}
  point3 = {a+sunkenWidth, a+sunkenWidth}
  point3[4] = bulge
  point4 = {X-a-sunkenWidth, a+sunkenWidth}
  points = {point1,point2,point3,point4,point1}
  G.polylineimp(points)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(points)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,10))
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(points)
  
  return true
end

require "ADekoDebugMode"
-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xLimit = 300
  yLimit = 300
  xMin = 140
  yMin = 140
  
  a = 70
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local sunkenDepth1 = 2
  local sunkenDepth2 = 4
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth2 = sunkenDepth2*math.tan((math.pi*vMidAngle/180)/2.0)
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("Cep_Acma")
  G.setThickness((-finalDepth))
  distance = sunkenWidth2+a+sunkenWidth1
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1, point2)
  
  G.setLayer("K_AciliV45")
  G.setThickness(-sunkenDepth2)
  point1 = {0,0}
  point2 = {X,Y}
  G.rectangle(point1,point2)
  
  G.setThickness(0)
  distance = sunkenWidth2+a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  local corner1, corner2 = G.sunkenFrame(point1,point2,sunkenDepth1,vMidAngle,vMidDiameter)
  corner1[3]= 0
  corner2[3]= 0
  
  G.setLayer("K_Freze5mm")
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,finalDepth,cThinDiameter)
  
  return true
end

require "ADekoDebugMode"
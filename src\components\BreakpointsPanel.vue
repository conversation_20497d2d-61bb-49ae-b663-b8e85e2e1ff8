<template>
  <div class="breakpoints-panel h-full flex flex-col bg-gray-50 border-l border-gray-200">
    <!-- Header -->
    <div class="flex items-center justify-between p-3 border-b border-gray-200 bg-white">
      <div class="flex items-center space-x-2">
        <Circle :size="16" class="text-red-500" />
        <h3 class="text-sm font-medium text-gray-700">Breakpoints</h3>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          {{ breakpoints.length }}
        </span>
      </div>
      <div class="flex items-center space-x-1">
        <button
          @click="removeAllBreakpoints"
          :disabled="breakpoints.length === 0"
          class="p-1 text-gray-400 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          title="Remove All Breakpoints"
        >
          <Trash2 :size="14" />
        </button>
        <button
          @click="togglePanel"
          class="p-1 text-gray-400 hover:text-gray-600"
          title="Close Breakpoints Panel"
        >
          <X :size="14" />
        </button>
      </div>
    </div>

    <!-- Breakpoints List -->
    <div class="flex-1 overflow-y-auto">
      <div v-if="breakpoints.length === 0" class="p-4 text-center text-gray-500 text-sm">
        <Circle :size="32" class="mx-auto mb-2 text-gray-300" />
        <p>No breakpoints set</p>
        <p class="text-xs mt-1">Click in the gutter next to line numbers to add breakpoints</p>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="breakpoint in sortedBreakpoints"
          :key="breakpoint.id"
          class="p-3 hover:bg-gray-100 cursor-pointer group"
          @click="goToBreakpoint(breakpoint)"
        >
          <div class="flex items-start space-x-3">
            <!-- Breakpoint Status -->
            <button
              @click.stop="toggleBreakpointEnabled(breakpoint)"
              class="mt-0.5 flex-shrink-0"
              :title="breakpoint.enabled ? 'Disable Breakpoint' : 'Enable Breakpoint'"
            >
              <Circle
                :size="12"
                :class="breakpoint.enabled ? 'text-red-500 fill-current' : 'text-gray-400'"
              />
            </button>

            <!-- Breakpoint Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-900">
                  {{ getFileName(breakpoint.filePath) }}
                </span>
                <span class="text-xs text-gray-500">
                  Line {{ breakpoint.lineNumber }}
                </span>
              </div>
              
              <div class="text-xs text-gray-500 mt-1 truncate">
                {{ breakpoint.filePath }}
              </div>
              
              <div v-if="breakpoint.condition" class="text-xs text-blue-600 mt-1 font-mono">
                Condition: {{ breakpoint.condition }}
              </div>
              
              <div v-if="breakpoint.hitCount" class="text-xs text-green-600 mt-1">
                Hit count: {{ breakpoint.hitCount }}
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                @click.stop="editCondition(breakpoint)"
                class="p-1 text-gray-400 hover:text-blue-500"
                title="Edit Condition"
              >
                <Edit3 :size="12" />
              </button>
              <button
                @click.stop="removeBreakpoint(breakpoint)"
                class="p-1 text-gray-400 hover:text-red-500"
                title="Remove Breakpoint"
              >
                <X :size="12" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Condition Edit Modal -->
    <div
      v-if="editingBreakpoint"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="cancelEdit"
    >
      <div
        class="bg-white rounded-lg p-4 w-96 max-w-full mx-4"
        @click.stop
      >
        <h4 class="text-sm font-medium text-gray-900 mb-3">
          Edit Breakpoint Condition
        </h4>
        <p class="text-xs text-gray-500 mb-3">
          {{ getFileName(editingBreakpoint.filePath) }}, Line {{ editingBreakpoint.lineNumber }}
        </p>
        <input
          ref="conditionInput"
          v-model="conditionText"
          type="text"
          placeholder="Enter condition (e.g., x > 10)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          @keydown.enter="saveCondition"
          @keydown.escape="cancelEdit"
        />
        <div class="flex justify-end space-x-2 mt-4">
          <button
            @click="cancelEdit"
            class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            @click="saveCondition"
            class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { Circle, Trash2, X, Edit3 } from 'lucide-vue-next'
import { breakpointService, type Breakpoint } from '../services/breakpointService'

// Define emits
const emit = defineEmits<{
  'close': []
  'goto-breakpoint': [filePath: string, lineNumber: number]
}>()

// Reactive data
const breakpoints = ref<Breakpoint[]>([])
const editingBreakpoint = ref<Breakpoint | null>(null)
const conditionText = ref('')
const conditionInput = ref<HTMLInputElement>()

// Computed
const sortedBreakpoints = computed(() => {
  return [...breakpoints.value].sort((a, b) => {
    // Sort by file path first, then by line number
    if (a.filePath !== b.filePath) {
      return a.filePath.localeCompare(b.filePath)
    }
    return a.lineNumber - b.lineNumber
  })
})

// Methods
const getFileName = (filePath: string): string => {
  const parts = filePath.split(/[/\\]/)
  return parts[parts.length - 1]
}

const togglePanel = (): void => {
  emit('close')
}

const goToBreakpoint = (breakpoint: Breakpoint): void => {
  emit('goto-breakpoint', breakpoint.filePath, breakpoint.lineNumber)
}

const toggleBreakpointEnabled = (breakpoint: Breakpoint): void => {
  breakpointService.setBreakpointEnabled(breakpoint.id, !breakpoint.enabled)
}

const removeBreakpoint = (breakpoint: Breakpoint): void => {
  breakpointService.removeBreakpoint(breakpoint.id)
}

const removeAllBreakpoints = (): void => {
  if (confirm('Remove all breakpoints?')) {
    breakpointService.removeAllBreakpoints()
  }
}

const editCondition = (breakpoint: Breakpoint): void => {
  editingBreakpoint.value = breakpoint
  conditionText.value = breakpoint.condition || ''

  nextTick(() => {
    conditionInput.value?.focus()
    conditionInput.value?.select()
  })
}

const saveCondition = (): void => {
  if (editingBreakpoint.value) {
    const condition = conditionText.value.trim() || undefined
    breakpointService.setBreakpointCondition(editingBreakpoint.value.id, condition)
  }
  cancelEdit()
}

const cancelEdit = (): void => {
  editingBreakpoint.value = null
  conditionText.value = ''
}

// Breakpoint listener
const updateBreakpoints = (newBreakpoints: Breakpoint[]): void => {
  breakpoints.value = newBreakpoints
}

// Lifecycle
onMounted(() => {
  // Get initial breakpoints
  breakpoints.value = breakpointService.getAllBreakpoints()
  
  // Listen for changes
  breakpointService.addListener(updateBreakpoints)
})

onUnmounted(() => {
  breakpointService.removeListener(updateBreakpoints)
})
</script>

<style scoped>
.breakpoints-panel {
  min-width: 250px;
  max-width: 400px;
}
</style>

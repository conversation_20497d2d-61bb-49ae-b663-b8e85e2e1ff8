-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script ADEL_B
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xMin = 250
  yMin = 250
  
  a = 70
  d = 6    --distance between sunkenframe and external frame

  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true 
  end
  
  
  
  local finalDepth = 5
  local pb      = a - d
  local corner1 = {a,a}
  local corner2 = {X-a,Y-a}
  local corner3 = {pb, pb}
  local corner4 = {X-pb, Y-pb}
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze6mm_Ic_SF")
  G.setThickness(-materialThickness)
  G.rectangle(corner1, corner2)
  
  <PERSON><PERSON>setLayer("H_Freze6mm_Ic_SF")
  G.setThickness(-finalDepth)
  G.rectangle(corner3, corner4)
  
  G.showPar({X,a+20},{X-a,a+20},"a",2)
  G.showPar({X-a,a+50},{X-pb,a+50},"d",2)

  return true
  
  
end
require "ADekoDebugMode"
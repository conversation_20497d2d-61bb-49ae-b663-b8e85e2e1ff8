-- GeometryDebug.lua
-- Comprehensive debugging utilities for geometry dimensions, positioning, and coordinate systems
-- Author: Augment Agent
-- Version: 1.0.0

local GeometryDebug = {}

-- Debug configuration
GeometryDebug.config = {
    enabled = true,
    verbose = false,
    show_coordinates = true,
    show_dimensions = true,
    show_transformations = true,
    show_bounds = true,
    precision = 2, -- decimal places for output
    color_debug = "#FF0000", -- red for debug overlays
    color_grid = "#CCCCCC", -- light gray for grid
    color_bounds = "#00FF00", -- green for bounds
    color_center = "#0000FF", -- blue for center points
}

-- Internal state tracking
GeometryDebug.state = {
    current_bounds = {min_x = math.huge, min_y = math.huge, max_x = -math.huge, max_y = -math.huge},
    geometry_count = 0,
    last_position = {x = 0, y = 0},
    coordinate_system = "standard", -- "standard" or "adeko"
    transformations = {},
    layers = {},
}

-- Utility function to format numbers with precision
function GeometryDebug.formatNumber(num, precision)
    precision = precision or GeometryDebug.config.precision
    return string.format("%." .. precision .. "f", num)
end

-- Print debug information with formatting
function GeometryDebug.debugPrint(category, message, data)
    if not GeometryDebug.config.enabled then return end
    
    local prefix = "🔍 [" .. category .. "] "
    local output = prefix .. message
    
    if data then
        if type(data) == "table" then
            output = output .. " " .. GeometryDebug.tableToString(data)
        else
            output = output .. " " .. tostring(data)
        end
    end
    
    print(output)
end

-- Convert table to readable string
function GeometryDebug.tableToString(t, indent)
    indent = indent or 0
    local result = {}
    local spacing = string.rep("  ", indent)
    
    if type(t) ~= "table" then
        return tostring(t)
    end
    
    for k, v in pairs(t) do
        local key = tostring(k)
        local value
        
        if type(v) == "table" then
            value = "{\n" .. GeometryDebug.tableToString(v, indent + 1) .. "\n" .. spacing .. "}"
        else
            value = tostring(v)
        end
        
        table.insert(result, spacing .. key .. " = " .. value)
    end
    
    return table.concat(result, ",\n")
end

-- Debug a point with optional label
function GeometryDebug.debugPoint(point, label)
    if not GeometryDebug.config.enabled then return end
    
    local x, y = point[1] or point.x or 0, point[2] or point.y or 0
    local z = point[3] or point.z
    
    local coords = "(" .. GeometryDebug.formatNumber(x) .. ", " .. GeometryDebug.formatNumber(y)
    if z then
        coords = coords .. ", " .. GeometryDebug.formatNumber(z)
    end
    coords = coords .. ")"
    
    local message = (label or "Point") .. ": " .. coords
    GeometryDebug.debugPrint("POINT", message)
    
    -- Update bounds
    GeometryDebug.updateBounds(x, y)
    
    return point
end

-- Debug a rectangle with dimensions
function GeometryDebug.debugRectangle(p1, p2, label)
    if not GeometryDebug.config.enabled then return end
    
    local x1, y1 = p1[1] or p1.x or 0, p1[2] or p1.y or 0
    local x2, y2 = p2[1] or p2.x or 0, p2[2] or p2.y or 0
    
    local width = math.abs(x2 - x1)
    local height = math.abs(y2 - y1)
    local area = width * height
    
    local message = (label or "Rectangle") .. ": " ..
        "P1(" .. GeometryDebug.formatNumber(x1) .. ", " .. GeometryDebug.formatNumber(y1) .. ") " ..
        "P2(" .. GeometryDebug.formatNumber(x2) .. ", " .. GeometryDebug.formatNumber(y2) .. ") " ..
        "Size(" .. GeometryDebug.formatNumber(width) .. " × " .. GeometryDebug.formatNumber(height) .. ") " ..
        "Area(" .. GeometryDebug.formatNumber(area) .. ")"
    
    GeometryDebug.debugPrint("RECTANGLE", message)
    
    -- Update bounds
    GeometryDebug.updateBounds(x1, y1)
    GeometryDebug.updateBounds(x2, y2)
    
    return {p1 = p1, p2 = p2, width = width, height = height, area = area}
end

-- Debug a circle with radius and area
function GeometryDebug.debugCircle(center, radius, label)
    if not GeometryDebug.config.enabled then return end
    
    local x, y = center[1] or center.x or 0, center[2] or center.y or 0
    local area = math.pi * radius * radius
    local circumference = 2 * math.pi * radius
    
    local message = (label or "Circle") .. ": " ..
        "Center(" .. GeometryDebug.formatNumber(x) .. ", " .. GeometryDebug.formatNumber(y) .. ") " ..
        "Radius(" .. GeometryDebug.formatNumber(radius) .. ") " ..
        "Area(" .. GeometryDebug.formatNumber(area) .. ") " ..
        "Circumference(" .. GeometryDebug.formatNumber(circumference) .. ")"
    
    GeometryDebug.debugPrint("CIRCLE", message)
    
    -- Update bounds with circle extents
    GeometryDebug.updateBounds(x - radius, y - radius)
    GeometryDebug.updateBounds(x + radius, y + radius)
    
    return {center = center, radius = radius, area = area, circumference = circumference}
end

-- Debug an arc with angles
function GeometryDebug.debugArc(center, radius, start_angle, end_angle, label)
    if not GeometryDebug.config.enabled then return end
    
    local x, y = center[1] or center.x or 0, center[2] or center.y or 0
    local angle_span = math.abs(end_angle - start_angle)
    local arc_length = (angle_span / 360) * 2 * math.pi * radius
    
    local message = (label or "Arc") .. ": " ..
        "Center(" .. GeometryDebug.formatNumber(x) .. ", " .. GeometryDebug.formatNumber(y) .. ") " ..
        "Radius(" .. GeometryDebug.formatNumber(radius) .. ") " ..
        "Angles(" .. GeometryDebug.formatNumber(start_angle) .. "° to " .. GeometryDebug.formatNumber(end_angle) .. "°) " ..
        "Span(" .. GeometryDebug.formatNumber(angle_span) .. "°) " ..
        "Length(" .. GeometryDebug.formatNumber(arc_length) .. ")"
    
    GeometryDebug.debugPrint("ARC", message)
    
    return {center = center, radius = radius, start_angle = start_angle, end_angle = end_angle, arc_length = arc_length}
end

-- Update bounding box
function GeometryDebug.updateBounds(x, y)
    local bounds = GeometryDebug.state.current_bounds
    bounds.min_x = math.min(bounds.min_x, x)
    bounds.min_y = math.min(bounds.min_y, y)
    bounds.max_x = math.max(bounds.max_x, x)
    bounds.max_y = math.max(bounds.max_y, y)
end

-- Get current bounding box
function GeometryDebug.getBounds()
    local bounds = GeometryDebug.state.current_bounds
    if bounds.min_x == math.huge then
        return nil -- No geometry has been processed
    end
    
    return {
        min_x = bounds.min_x,
        min_y = bounds.min_y,
        max_x = bounds.max_x,
        max_y = bounds.max_y,
        width = bounds.max_x - bounds.min_x,
        height = bounds.max_y - bounds.min_y,
        center_x = (bounds.min_x + bounds.max_x) / 2,
        center_y = (bounds.min_y + bounds.max_y) / 2
    }
end

-- Print current bounds information
function GeometryDebug.printBounds()
    local bounds = GeometryDebug.getBounds()
    if not bounds then
        GeometryDebug.debugPrint("BOUNDS", "No geometry bounds available")
        return
    end
    
    local message = "Min(" .. GeometryDebug.formatNumber(bounds.min_x) .. ", " .. GeometryDebug.formatNumber(bounds.min_y) .. ") " ..
        "Max(" .. GeometryDebug.formatNumber(bounds.max_x) .. ", " .. GeometryDebug.formatNumber(bounds.max_y) .. ") " ..
        "Size(" .. GeometryDebug.formatNumber(bounds.width) .. " × " .. GeometryDebug.formatNumber(bounds.height) .. ") " ..
        "Center(" .. GeometryDebug.formatNumber(bounds.center_x) .. ", " .. GeometryDebug.formatNumber(bounds.center_y) .. ")"
    
    GeometryDebug.debugPrint("BOUNDS", message)
    return bounds
end

-- Reset debug state
function GeometryDebug.reset()
    GeometryDebug.state.current_bounds = {min_x = math.huge, min_y = math.huge, max_x = -math.huge, max_y = -math.huge}
    GeometryDebug.state.geometry_count = 0
    GeometryDebug.state.last_position = {x = 0, y = 0}
    GeometryDebug.state.transformations = {}
    GeometryDebug.state.layers = {}
    GeometryDebug.debugPrint("SYSTEM", "Debug state reset")
end

-- Debug coordinate system information
function GeometryDebug.debugCoordinateSystem()
    local message = "Coordinate System: " .. GeometryDebug.state.coordinate_system
    if GeometryDebug.state.coordinate_system == "adeko" then
        message = message .. " (Origin: bottom-left, Y+ up)"
    else
        message = message .. " (Origin: top-left, Y+ down)"
    end
    GeometryDebug.debugPrint("COORDS", message)
end

-- Set coordinate system
function GeometryDebug.setCoordinateSystem(system)
    GeometryDebug.state.coordinate_system = system
    GeometryDebug.debugCoordinateSystem()
end

-- Debug layer information
function GeometryDebug.debugLayer(layer_name, thickness, face)
    local message = "Layer: " .. (layer_name or "default")
    if thickness then
        message = message .. " Thickness: " .. GeometryDebug.formatNumber(thickness)
    end
    if face then
        message = message .. " Face: " .. face
    end
    GeometryDebug.debugPrint("LAYER", message)
    
    -- Track layer usage
    if layer_name then
        GeometryDebug.state.layers[layer_name] = (GeometryDebug.state.layers[layer_name] or 0) + 1
    end
end

-- Print layer usage summary
function GeometryDebug.printLayerSummary()
    GeometryDebug.debugPrint("LAYERS", "Layer usage summary:")
    for layer, count in pairs(GeometryDebug.state.layers) do
        print("  " .. layer .. ": " .. count .. " operations")
    end
end

-- Enable/disable debugging
function GeometryDebug.enable()
    GeometryDebug.config.enabled = true
    GeometryDebug.debugPrint("SYSTEM", "Geometry debugging enabled")
end

function GeometryDebug.disable()
    GeometryDebug.config.enabled = false
end

-- Set verbosity level
function GeometryDebug.setVerbose(verbose)
    GeometryDebug.config.verbose = verbose
    GeometryDebug.debugPrint("SYSTEM", "Verbose mode: " .. (verbose and "ON" or "OFF"))
end

-- Debug transformation matrix
function GeometryDebug.debugTransformation(matrix, label)
    if not GeometryDebug.config.enabled then return end

    local message = (label or "Transformation") .. ": "
    if type(matrix) == "table" then
        if #matrix == 6 then -- 2D transformation matrix
            message = message .. string.format("2D Matrix [%.3f, %.3f, %.3f, %.3f, %.3f, %.3f]",
                matrix[1], matrix[2], matrix[3], matrix[4], matrix[5], matrix[6])
        elseif #matrix == 16 then -- 4x4 3D transformation matrix
            message = message .. "3D Matrix [4x4]"
        else
            message = message .. "Custom Matrix [" .. #matrix .. " elements]"
        end
    else
        message = message .. tostring(matrix)
    end

    GeometryDebug.debugPrint("TRANSFORM", message)
    table.insert(GeometryDebug.state.transformations, {matrix = matrix, label = label})
end

-- Debug polyline/polygon with all points
function GeometryDebug.debugPolyline(points, label, closed)
    if not GeometryDebug.config.enabled then return end

    local point_count = #points
    local total_length = 0

    -- Calculate total length
    for i = 1, point_count - 1 do
        local p1 = points[i]
        local p2 = points[i + 1]
        local dx = (p2[1] or p2.x) - (p1[1] or p1.x)
        local dy = (p2[2] or p2.y) - (p1[2] or p1.y)
        total_length = total_length + math.sqrt(dx * dx + dy * dy)
    end

    -- Add closing segment if closed
    if closed and point_count > 2 then
        local p1 = points[point_count]
        local p2 = points[1]
        local dx = (p2[1] or p2.x) - (p1[1] or p1.x)
        local dy = (p2[2] or p2.y) - (p1[2] or p1.y)
        total_length = total_length + math.sqrt(dx * dx + dy * dy)
    end

    local message = (label or "Polyline") .. ": " ..
        point_count .. " points, " ..
        "Length: " .. GeometryDebug.formatNumber(total_length) ..
        (closed and " (closed)" or " (open)")

    GeometryDebug.debugPrint("POLYLINE", message)

    -- Debug individual points if verbose
    if GeometryDebug.config.verbose then
        for i, point in ipairs(points) do
            GeometryDebug.debugPoint(point, "Point " .. i)
        end
    end

    -- Update bounds with all points
    for _, point in ipairs(points) do
        local x, y = point[1] or point.x or 0, point[2] or point.y or 0
        GeometryDebug.updateBounds(x, y)
    end

    return {points = points, length = total_length, closed = closed}
end

-- Debug distance between two points
function GeometryDebug.debugDistance(p1, p2, label)
    if not GeometryDebug.config.enabled then return end

    local x1, y1 = p1[1] or p1.x or 0, p1[2] or p1.y or 0
    local x2, y2 = p2[1] or p2.x or 0, p2[2] or p2.y or 0
    local z1, z2 = p1[3] or p1.z, p2[3] or p2.z

    local dx = x2 - x1
    local dy = y2 - y1
    local distance_2d = math.sqrt(dx * dx + dy * dy)

    local message = (label or "Distance") .. ": " .. GeometryDebug.formatNumber(distance_2d)

    if z1 and z2 then
        local dz = z2 - z1
        local distance_3d = math.sqrt(dx * dx + dy * dy + dz * dz)
        message = message .. " (2D), " .. GeometryDebug.formatNumber(distance_3d) .. " (3D)"
    end

    GeometryDebug.debugPrint("DISTANCE", message)
    return distance_2d
end

-- Debug angle between two vectors
function GeometryDebug.debugAngle(p1, p2, p3, label)
    if not GeometryDebug.config.enabled then return end

    -- Calculate vectors from p2 to p1 and p2 to p3
    local x1, y1 = p1[1] or p1.x or 0, p1[2] or p1.y or 0
    local x2, y2 = p2[1] or p2.x or 0, p2[2] or p2.y or 0
    local x3, y3 = p3[1] or p3.x or 0, p3[2] or p3.y or 0

    local v1x, v1y = x1 - x2, y1 - y2
    local v2x, v2y = x3 - x2, y3 - y2

    local dot = v1x * v2x + v1y * v2y
    local mag1 = math.sqrt(v1x * v1x + v1y * v1y)
    local mag2 = math.sqrt(v2x * v2x + v2y * v2y)

    local angle_rad = math.acos(dot / (mag1 * mag2))
    local angle_deg = math.deg(angle_rad)

    local message = (label or "Angle") .. ": " ..
        GeometryDebug.formatNumber(angle_deg) .. "° (" ..
        GeometryDebug.formatNumber(angle_rad) .. " rad)"

    GeometryDebug.debugPrint("ANGLE", message)
    return angle_deg
end

-- Create visual debug markers (for canvas rendering)
function GeometryDebug.createDebugMarker(x, y, marker_type, label)
    if not GeometryDebug.config.enabled then return end

    -- This would integrate with the graphics system to create visual markers
    local marker = {
        x = x,
        y = y,
        type = marker_type or "point",
        label = label,
        color = GeometryDebug.config.color_debug
    }

    GeometryDebug.debugPrint("MARKER", "Created " .. marker.type .. " marker at (" ..
        GeometryDebug.formatNumber(x) .. ", " .. GeometryDebug.formatNumber(y) .. ")" ..
        (label and " - " .. label or ""))

    return marker
end

-- Print debug summary
function GeometryDebug.printSummary()
    GeometryDebug.debugPrint("SUMMARY", "=== GEOMETRY DEBUG SUMMARY ===")
    GeometryDebug.debugCoordinateSystem()
    GeometryDebug.printBounds()
    GeometryDebug.printLayerSummary()
    GeometryDebug.debugPrint("SUMMARY", "Total geometry operations: " .. GeometryDebug.state.geometry_count)
    GeometryDebug.debugPrint("SUMMARY", "Total transformations: " .. #GeometryDebug.state.transformations)
    GeometryDebug.debugPrint("SUMMARY", "=== END SUMMARY ===")
end

-- Integration helpers for ADekoLib functions
GeometryDebug.wrappers = {}

-- Wrapper for ADekoLib.rectangle to add debugging
function GeometryDebug.wrappers.rectangle(original_func)
    return function(p1, p2, ...)
        local result = original_func(p1, p2, ...)
        GeometryDebug.debugRectangle(p1, p2, "ADekoLib.rectangle")
        GeometryDebug.state.geometry_count = GeometryDebug.state.geometry_count + 1
        return result
    end
end

-- Wrapper for ADekoLib.circle to add debugging
function GeometryDebug.wrappers.circle(original_func)
    return function(center, radius, ...)
        local result = original_func(center, radius, ...)
        GeometryDebug.debugCircle(center, radius, "ADekoLib.circle")
        GeometryDebug.state.geometry_count = GeometryDebug.state.geometry_count + 1
        return result
    end
end

return GeometryDebug

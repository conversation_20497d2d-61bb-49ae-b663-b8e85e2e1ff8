-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xMin = 250
  yMin = 250
  
  h = 25
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vMidAngle = 90
  local vWideAngle = 150
  local vMidDiameter = 40
  local vWideDiameter = 75
  local notchWidth = 2.5
  local finalDepth = 8
  local notchDepth = 2
  local sunkenWidth = finalDepth * math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth2 = finalDepth * math.tan((math.pi*vWideAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV15")		--Angled surfaces
  G.setThickness(-finalDepth)
  G.rectangle({0,0},{X,Y})
  G.rectangle({sunkenWidth2+sunkenWidth+h, sunkenWidth2+sunkenWidth+h}, {X-(sunkenWidth2+sunkenWidth+h), Y-(sunkenWidth2+sunkenWidth+h)})
  
  G.setLayer("K_AciliV45")
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({sunkenWidth2, sunkenWidth2}, {X-sunkenWidth2, Y-sunkenWidth2}, finalDepth, vMidAngle, vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setLayer("Cep_Acma")
  G.setThickness(-notchDepth)
  distance = sunkenWidth2+sunkenWidth+h+sunkenWidth2+notchWidth
  G.rectangle({distance,distance},{X-distance,Y-distance})
  
  G.setLayer("K_Freze10mm")		--Channel cleaning
  G.setThickness(-finalDepth)
  
  distance = sunkenWidth2+sunkenWidth+cMidDiameter/2
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  if h > cMidDiameter then
    distance = sunkenWidth2+sunkenWidth+h-cMidDiameter/2
    point3 = {distance, distance}
    point4 = {X-distance, Y-distance}
    G.rectangle(point3,point4) 
  
    k = (h-cMidDiameter)/(cMidDiameter/2)
    
    for i=1, k, 1 do
      point1 = G.ptAdd(point1,{cMidDiameter/2,cMidDiameter/2})
      point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
      if point1[1]>point3[1]-cMidDiameter/2 then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setLayer("K_Freze5mm")		--Corner cleaning
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,finalDepth,cThinDiameter)
  distance = sunkenWidth2+sunkenWidth+h+sunkenWidth2+notchWidth
  G.cleanCorners({distance,distance},{X-distance,Y-distance},notchDepth,cThinDiameter)
    
  return true
end


require "ADekoDebugMode"

-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xLimit = 247
  yLimit = 247
  xMin = 140
  yMin = 140
  formtool = 0
  
  g = 50
  h = 30
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2.5
  local sunkenDepth1 = 2.5
  local finalDepth = 10
  local sunkenDepth3 = 7
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV45")
  if Y>yLimit and X>xLimit then		--Lines
    G.setThickness(-sunkenDepth1)
    point1 = {g+sunkenWidth1,0}
    point2 = {g+sunkenWidth1,Y}
    G.line(point1,point2)
    
    point1 = {X-(g+sunkenWidth1),0}
    point2 = {X-(g+sunkenWidth1),Y}
    G.line(point1,point2)
  end
  
  if formtool == 1 then
    G.setLayer("K_Desen")
  end
  
  G.setThickness(-finalDepth)
  point1 = {0,0}
  point2 = {X,Y}
  G.rectangle(point1,point2)
  
  G.setLayer("K_Freze10mm")
  if Y>yLimit and X>xLimit then		--mid depth
    G.setThickness(-sunkenDepth3)
    distance = g+sunkenWidth1+cMidDiameter/2
    point1 = {distance, distance}
    point2 = {X-distance, Y-distance}
    G.rectangle(point1,point2)
  
    if h > cMidDiameter then
      distance = g+sunkenWidth1+h-cMidDiameter/2
      point3 = {distance, distance}
      point4 = {X-distance, Y-distance}
      G.rectangle(point3,point4) 
    
      k = (h-cMidDiameter)/(cMidDiameter/2)
      
      for i=1, k, 1 do
        point1 = G.ptAdd(point1,{cMidDiameter/2,cMidDiameter/2})
        point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
        if point1[1]>point3[1]-cMidDiameter/2 then
          break
        end
        G.rectangle(point1,point2)
      end
    end
  end
  
  G.setLayer("K_AciliV15")
  if Y>yLimit and X>xLimit then		--mid shape and notches
    G.setThickness(-sunkenDepth3)
    distance = g+sunkenWidth1+h
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_Freze5mm")
  G.setThickness(0)
  if Y>yLimit and X>xLimit then		--corner cleaning
    distance = g+sunkenWidth1
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.cleanCorners(point1,point2,sunkenDepth3,cThinDiameter)
  end
  
  return true
end

require "ADekoDebugMode"
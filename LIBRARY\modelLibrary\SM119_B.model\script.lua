-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 30
  b = 100
  
  G = ADekoLib
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
 
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  <PERSON>.setLayer("K_Top10mm")
  G.setThickness(-5)
  local p = {}
  p[1]  = {a, a}
  p[2]  = {2*a, a}
  p[3]  = {2*a, Y-a}
  p[4]  = {a, Y-a}
  p[5]  = {a, Y-2*a}
  p[6]  = {X-a, Y-2*a}
  p[7]  = {X-a, Y-a}
  p[8]  = {X-2*a, Y-a}
  p[9]  = {X-2*a, a}
  p[10] = {X-a, a}
  p[11] = {X-a, 2*a}
  p[12] = {a, 2*a}
  p[13] = G.deepcopy(p[1])
  G.polylineimp(p)
  
  G.setLayer("K_Top20mm")
  G.setThickness(-5)
  G.rectangle({b, b}, {X-b, Y-b})
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+5)
  G.rectangle({b, b}, {X-b, Y-b})
  
  G.setFace("bottom")
  G.setThickness(-6)
  G.setLayer("H_Freze20mm_Ic_SF")
  local glassMargin = 10
  G.rectangle({b-glassMargin, b-glassMargin}, {X-b+glassMargin, Y-b+glassMargin})
  
  return true
end

require "ADekoDebugMode"
-- Simple test script for OpenCascade.js 3D visualization
-- Creates a basic door panel only

-- Material thickness
materialThickness = 18

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = AdekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    print("Simple door panel created")
    print("Switch to 3D tab to see OpenCascade.js model")
end

-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  a = 70
  spacing = 70
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  local vNarrowAngle = 60
  local vNarrowDiameter = 30
  local sunkenWidth = finalDepth*math.tan((math.pi*vNarrowAngle/180)/2.0)
	
  if X < 150 or Y < 120 then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_AciliV60")
  G.setThickness(-finalDepth)
  
  local point1 = {X-a-sunkenWidth, Y-a-sunkenWidth, 0, 0}
  local bulge = G.bulge(point1, {X/2, Y-a/2-sunkenWidth}, {a+sunkenWidth, Y-a-sunkenWidth})
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
	point1[4] = bulge
	local point2 = {a+sunkenWidth, Y-a-sunkenWidth, 0, 0}
	point3 = {a+sunkenWidth, a+sunkenWidth}
	point4 = {X-a-sunkenWidth, a+sunkenWidth}
	G.line(point1, point2, bulge)		-- make the arcs
	G.line(point3, point4, bulge)
	local radius = G.radius(point1, point2, bulge)
	local sunkenDepth = G.distance(point1, point2)
	local nLines = math.floor((X-2*a-2*sunkenWidth)/spacing)
	local modifiedSpacing = (X-2*a-2*sunkenWidth)/nLines
	for i=0, nLines, 1		-- loop for the vertical lines
	do
		local Lp1 = {a+sunkenWidth+i*modifiedSpacing, 0}		-- imaginary lines to intersect the above arc
		local Lp2 = {a+sunkenWidth+i*modifiedSpacing, Y}
		comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		-- find center of the arc
		if (comment=='tangent' or comment=='intersection') then
			comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		-- find line-arc intersection point
			if (comment2=='tangent' or comment2=='secant') then
        G.line({intersection1[1], intersection1[2]}, {intersection1[1], Y-intersection1[2]})
			end
		else
			G.error()
      return false
		end
	end
  
  return true
end

require "ADekoDebugMode"
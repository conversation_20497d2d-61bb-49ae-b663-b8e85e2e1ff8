-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xMin = 140
  yMin = 140
  
  g = 40
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local offset = 5
  local notchWidth = 2.5
  local notchDepth = 2.5
  local sunkenDepth1 = 2.5
  local sunkenDepth2 = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth2 = sunkenDepth2*math.tan((math.pi*vMidAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-(2*notchDepth+sunkenDepth1))
  
  --Outermost notch
  
  p11r = {offset,offset}
  p21r = {X-offset,Y-offset}
  G.rectangle(p11r,p21r)
    
  G.setThickness(-notchDepth)
  
  --Block inner notches
  
  distance = offset+sunkenWidth1+notchWidth
  p12r = {distance,distance} 
  p22r = {X-distance,Y-distance}
  G.rectangle(p12r,p22r)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-(notchDepth/2+sunkenDepth2))
  
  --First outer notch
  
  distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2 
  p13r = {distance,distance} 
  p23r = {X-distance,Y-distance}
  G.rectangle(p13r,p23r)
  
  G.setThickness(-windowDepthFront)
  distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth+sunkenWidth1
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV45")
  G.setThickness(-(notchDepth/2))
    
  distance = offset+sunkenWidth1+notchWidth+g
  
  --First inner sunkenFrame
  
  p11a = {distance,distance}
  p21a = {X-distance,Y-distance}
  corner1,corner2 =  G.sunkenFrame(p11a,p21a,sunkenDepth2,vMidAngle,vMidDiameter)
  
  --Lines
  
  p13a = {p11a[1],offset+sunkenWidth1+notchWidth}
  G.line(p13a,p11a,0)
  
  p14a = {X-p13a[1],p13a[2]}
  p24a = {X-p11a[1],p11a[2]}
  G.line(p14a,p24a,0)
    
  p15a = {p13a[1],Y-p13a[2]}
  p25a = {p11a[1],Y-p11a[2]}
  G.line(p15a,p25a,0)
    
  p23a = {p21a[1],Y-offset-sunkenWidth1-notchWidth}
  G.line(p23a,p21a,0)
  
  G.setThickness(-(notchDepth/2+sunkenDepth2))
    
  distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth
  
  --Second inner sunkenFrame
    
  p12a = {distance,distance}
  p22a = {X-distance,Y-distance}
  c3,c4 =  G.sunkenFrame(p12a,p22a,sunkenDepth1,vMidAngle,vMidDiameter)
  
  G.setThickness(-(notchDepth+sunkenDepth1))
  
  --Outer most angled surface
  
  p16a = {offset,offset}
  p26a = {X-offset,Y-offset}
  G.rectangle(p16a,p26a)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  distance = offset+sunkenWidth1+notchWidth+g+sunkenWidth2+2*notchWidth+sunkenWidth1
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})

  return true
  
end

require "ADekoDebugMode"
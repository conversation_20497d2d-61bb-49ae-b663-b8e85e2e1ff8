-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  a = 50
  c = 5
  blockNoX = 2
  blockNoY = 3
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenDepth = 4
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  local stepX = (blockNoX-1)		--Number of laths on X axis
  local stepY = (blockNoY-1)		--Number of laths on Y axis
  local innerX = X-2*a-2*sunkenWidth-stepX*c-stepX*2*sunkenWidth		--Width of the inner rectangle on the x axis
  local innerY = Y-2*a-2*sunkenWidth-stepY*c-stepY*2*sunkenWidth		--Length of the inner rectangle on the y axis
  local correctedblockX = innerX / blockNoX
  local correctedblockY = innerY / blockNoY
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  for i=0, stepX, 1
  do
    G.setLayer("K_AciliV30")  -- windows
    G.setThickness(0)
    x1 = a+i*(2*sunkenWidth+correctedblockX+c)
    x2 = a+(i+1)*(2*sunkenWidth+correctedblockX)+i*c
    for j=0, stepY, 1
    do
      G.setLayer("K_AciliV30")  -- windows
      G.setThickness(0)
      y1 = a+j*(2*sunkenWidth+correctedblockY+c)
      y2 = a+(j+1)*(2*sunkenWidth+correctedblockY)+j*c
      point1 = {x1, y1}
      point2 = {x2,y2}
      local corner1,corner2 = G.sunkenFrame(point1, point2, sunkenDepth, vWideAngle, vWideDiameter)
      corner1[3], corner2[3] = 0, 0
      
      G.setLayer("H_Freze10mm_Ic")
      G.setThickness(-windowDepthFront)		--windows
      G.rectangle(corner1, corner2)
    end
  end
  
  G.setFace("bottom")
  
  G.setLayer("K_Freze20mm_SF")		--Clearing procedures of the backside
  for i = 1, stepX do
    G.setThickness(-windowDepthBack)
    local y = a+sunkenWidth
    local x = a+i*(2*sunkenWidth+correctedblockX)+((i*2)-1)*(c/2)
    point1 = {x,y}
    point2 = {x,Y-y}
    G.line(point1,point2,0)
  end
  
  for j = 1, stepY do
    G.setThickness(-windowDepthBack)
    local x = a+sunkenWidth
    local y = a+j*(2*sunkenWidth+correctedblockY)+((j*2)-1)*(c/2)
    point1 = {x,y}
    point2 = {X-x,y}
    G.line(point1,point2,0)
  end
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth-10, a+sunkenWidth-10}, {X-a-sunkenWidth+10, Y-a-sunkenWidth+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth, a+sunkenWidth}, {X-a-sunkenWidth, Y-a-sunkenWidth})
  return true
end

require "ADekoDebugMode"

-- Test script for OpenCascade.js 3D visualization
-- This script creates a simple door with basic operations

-- Material thickness
materialThickness = 18

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Basic cutting operation with cylindrical tool
    G.setLayer("K_Freze10mm")  -- 10mm cylindrical tool
    G.setThickness(-5)
    G.rectangle({50, 50}, {350, 100})

    -- 2. Another cutting operation
    G.setLayer("H_Freze20mm")  -- 20mm cylindrical tool for profiling
    G.setThickness(-3)
    G.circle({200, 200}, 30)

    -- 3. V-bit operation
    G<PERSON>setLayer("K_AciliV90")  -- V-bit tool
    G.setThickness(-2)
    <PERSON><PERSON>rectangle({50, 250}, {350, 280})

    -- BOTTOM SURFACE OPERATIONS
    G.setFace("bottom")

    -- 4. Bottom face cutting operation
    G.setLayer("H_Freze15mm_SF")  -- 15mm tool on bottom face
    G.setThickness(-4)
    G.rectangle({100, 150}, {300, 200})

    -- 5. Bottom face drilling
    G.setLayer("DRILL6mm_SF")  -- 6mm drill on bottom face
    G.setThickness(-8)
    G.circle({150, 300}, 3)
    G.circle({250, 300}, 3)

    print("Test door model created with PANEL layer and tool operations")
    print("Switch to 3D tab in visualization to see OpenCascade.js model")
end

require "ADekoDebugMode"
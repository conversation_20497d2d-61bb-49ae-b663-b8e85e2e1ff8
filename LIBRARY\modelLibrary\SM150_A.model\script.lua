-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  a = 50
  n = 2
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local radius = 0.24
  local depth = 8
  local origX = X
  local origY = Y
  X = 500
  Y = 700
  
  if (X<200 or Y<200) then
    return true
  end
  
  G.setLayer("K_Kut20mm_V30")
  G.setThickness(-depth)
  
  local p1 = {a, Y}
  local p2 = {X/2, Y-(1-2*radius)*X}
  local p3 = {X-a, Y}
  comment, dp12, p12 = G.circleLineIntersection(p1, radius*X, p1, p2)
  comment, p23, dp23 = G.circleLineIntersection(p3, radius*X, p2, p3)
  local rP2 = G.distance(p2, p12)
  comment, tepe, dik2 = G.circleLineIntersection(p2, rP2, p2, {p2[1], Y})
  local bulge = G.bulge(p12, tepe, p23)
  --G.line(p12, p23, bulge)    -- orta yay çizildi
  p22 = G.ptAdd(p2, {-X/4, 0})
  p222 = G.ptAdd(p2, {X/4, 0})
  comment, dtP1, tP1 = G.circleLineIntersection(p1, radius*X, p1, p22)
  comment, tP3, dtP3 = G.circleLineIntersection(p3, radius*X, p3, p222)
  local n1, n2 = {a, Y-radius*X}, {radius*X, Y}
  bulge = G.bulge(n1, tP1, p12)
  --G.line(n1, p12, bulge) -- soldaki küçük yay da oldu
  local m1, m2 = {X-a, Y-radius*X}, {X-radius*X-a, Y}
  bulge = G.bulge(m1, tP3, p23)
  --G.line(m1, p23, bulge) -- sagdaki yay
  
  local pointsOrta = G.circularArc(p2, 2*rP2, 500, 56, 127)
  local pointsSol  = G.circularArc(p1, 2*radius*X, 100, 270, 305)
  local pointsSag  = G.circularArc(p3, 2*radius*X, 100, 230, 270)

  local arcs  = G.joinPolylines(G.joinPolylines(pointsOrta, pointsSag), pointsSol)
  local scaledArcsTop = G.scaleVertical(G.scaleHorizontal(arcs, 2*a, origX-2*a), origY-n*a, origY-a)
  local final = G.joinPolylines(scaledArcsTop, {{origX-a, origY-n*a}, {origX-a, a}, {a, a}, {a, origY-n*a}, scaledArcsTop[1]})
  G.polylineimp(final)
  
  G.showPar({0, Y/2}, {a, Y/2}, "a")
  G.showPar({X/2, 0}, {X/2, a}, "a")
  G.showPar({X/2, Y}, {X/2, Y-a}, "a")
  G.showPar({1.1*a, Y}, {1.1*a, Y-n*a}, "n*a")
  
  return true
end

require "ADekoDebugMode"
-- <PERSON>ek<PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xMin = 140
  yMin = 140
  formTool = 0
  
  a = 8
  g = 40
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 1
  local sunkenDepth = 1
  local notchWidth = 2
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40 
  local sunkenWidth = sunkenDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  G.setLayer("H_Freze10mm_Dis")
  if formTool == 0 then
    --Outer notches
    G.setThickness(-(5*sunkenDepth+3*notchDepth))
    distance = sunkenWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(4*sunkenDepth+3*notchDepth))
    distance = 2*sunkenWidth+notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(3*sunkenDepth+2*notchDepth))
    distance = 3*sunkenWidth+2*notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(2*sunkenDepth+notchDepth))
    distance = 4*sunkenWidth+3*notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(sunkenDepth+notchDepth))
    distance = 5*sunkenWidth+4*notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
    
  G.setLayer("H_Freze10mm_Ic")		--Inner notches
  G.setThickness(-2*notchDepth)
  distance = 6*sunkenWidth+4*notchWidth+g+a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)

  G.setLayer("K_AciliV45")
  G.setThickness(-(6*sunkenDepth+3*notchDepth))
  if formTool == 0 then
	--Outer angled surfaces
    distance = 0
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(5*sunkenDepth+3*notchDepth))
    distance = sunkenWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(4*sunkenDepth+2*notchDepth))
    distance = 2*sunkenWidth+a+notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(3*sunkenDepth+notchDepth))
    distance = 3*sunkenWidth+2*notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(2*sunkenDepth+notchDepth))
    distance = 4*sunkenWidth+3*notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-(sunkenDepth))
    distance = 5*sunkenWidth+4*notchWidth+a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  else
    G.setLayer("K_Desen")
    distance = 0
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_AciliV45")		--Inner angled surfaces		
  G.setThickness(-2*notchDepth)
  distance = 6*sunkenWidth+7*notchWidth+g+a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  corner1,corner2 = G.sunkenFrame(point1,point2,5*sunkenDepth,vMidAngle,vMidDiameter)
    
  G.setLayer("K_Freze5mm")		--Corner cleaning
  G.setThickness(0)
  distance = 6*sunkenWidth+4*notchWidth+g+a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.cleanCorners(point1,point2,2*notchDepth,cThinDiameter)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = 11*sunkenWidth+7*notchWidth+g+a
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  
  return true
end

require "ADekoDebugMode"
-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 65                                    --Distance from the edges
  c = 18                                    --Width of the block laths
  
  print("Default model parameters are: ", a, "and", c)
  
  G = ADekoLib
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  print("Default model parameters are: ", a, "and", c)
  
  local blockNoX = 2                              --Number of blocks on the x axis
  local blockNoY = 2                              --Number of blocks on the y axis
  --local vOrtaAngle = 90                              -- derece cinsinden açı
  --local vOrtaDiameter = 40                            -- mm cinsinden takım çapı, verilmediği için yeterince geniş seçtim
  --local cInceDiameter = 5
  --local d = 4                                         -- mm cinsinden pencere derinliği
  --local t = 4
  --local m = d*math.tan((math.pi*vOrtaAngle/180)/2.0)  -- mm cinsinden, V çakısı ile açılmış vadi genişliğinin yarısı
  local stepX = (blockNoX-1)                      --Number of blocks on X axis
  local stepY = (blockNoY-1)                      --Number of blocks on Y axis
  --local innerX = X-2*a-2*m-stepX*c-stepX*2*m                  --Width of the inner rectangle on the x axis
  local innerX = X-2*a-stepX*c-stepX                  --Width of the inner rectangle on the x axis
  --local innerY = Y-2*a-2*m-stepY*c-stepY*2*m                  --Length of the inner rectangle on the y axis
  local innerY = Y-2*a-stepY*c-stepY                  --Length of the inner rectangle on the y axis
  local correctedblockX = innerX / blockNoX
  local correctedblockY = innerY / blockNoY
  
--  G.setLayer("K_AciliV45")
--  G.setThickness(-(d+t))
--  G.rectangle({0,0},{X,Y})
  
  for i=0, stepX, 1
  do
--    G.setLayer("K_AciliV45")  -- pencereler
--    G.setThickness(0)
    --x1 = a+i*(2*m+correctedblockX+c)
    x1 = a+i*(correctedblockX+c)
    --x2 = a+(i+1)*(2*m+correctedblockX)+i*c
    x2 = a+(i+1)*(correctedblockX)+i*c
    for j=0, stepY, 1
    do
--      G.setLayer("K_AciliV45")  -- pencereler
--      G.setThickness(0)
      --y1 = a+j*(2*m+correctedblockY+c)
      y1 = a+j*(correctedblockY+c)
      --y2 = a+(j+1)*(2*m+correctedblockY)+j*c
      y2 = a+(j+1)*(correctedblockY)+j*c
      p1 = {x1, y1}
      p2 = {x2,y2}
--      local corner1,corner2 = G.sunkenFrame(p1, p2, d, vOrtaAngle, vOrtaDiameter)
--      corner1[3], corner2[3] = 0, 0
      
      G.setLayer("H_Radus_Ic")
      G.setThickness(-materialThickness)  -- V'nin ortasından kes
      --G.rectangle(corner1, corner2)
      G.rectangle(p1, p2)
  
--	    G.setLayer("K_Freze5mm")
--	    G.setThickness(0)
--	    G.cleanCorners(corner1,corner2,7,cInceDiameter)
--	    G.cleanCorners(corner1,corner2,14,cInceDiameter)
    end
  end
  
  
  G.setFace("bottom")
  
--  G.setLayer("K_Freze20mm_SF")                          -- layer name, clearing procedures of the backside
--  for i = 1, stepX do
--    G.setThickness(-6)
--    local y = a+m
--    local x = a+i*(2*m+correctedblockX)+((i*2)-1)*(c/2)
--    p1 = {x,y}
--    p2 = {x,Y-y}
--    G.line(p1,p2,0)
--  end
  
--  for j = 1, stepY do
--    G.setThickness(-6)
--    local x = a+m
--    local y = a+j*(2*m+correctedblockY)+((j*2)-1)*(c/2)
--    p1 = {x,y}
--    p2 = {X-x,y}
--    G.line(p1,p2,0)
--  end
  
  G.setLayer("Cep_Ac")
  G.setThickness(-7)
  --G.rectangle({a+m-10, a+m-10}, {X-a-m+10, Y-a-m+10})
  G.rectangle({a-10, a-10}, {X-a+10, Y-a+10})
  
--  G.setLayer("K_Freze20mm_SF")
--  G.setThickness(-6)
--  G.rectangle({a+m, a+m}, {X-a-m, Y-a-m})
  return true
end

require "ADekoDebugMode"

-- Debug Sweep Operation Test
-- Simple test to debug the sweep operation

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions
AdekoLib.setPanelSize(100, 80, 18)

print("=== Debug Sweep Operation Test ===")

-- Create the door panel (base geometry)
print("Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door", 0, 0, 100, 80)

-- Create ONE simple tool operation to test
print("Creating single tool operation...")
AdekoLib.layer("8MM")
AdekoLib.rect("test_cut", 30, 30, 40, 20)

print("Geometry created!")
print("- Door panel: 100x80x18mm")
print("- Single 8mm endmill cut: 40x20mm rectangle")

-- Finish the model
AdekoLib.finish()

print("\n=== Debug Test Instructions ===")
print("1. Load this script in the application")
print("2. Run the script")
print("3. Switch to 3D visualization")
print("4. Check console for detailed sweep operation messages")
print("5. Look for these specific messages:")
print("   - '🔧 SWEEP OPERATION: Starting door processing with tools'")
print("   - '✅ Door body created successfully'")
print("   - '🔧 Creating BReps for X tools...'")
print("   - '🔧 Performing sweep operations...'")
print("   - '✅ Sweep operations completed'")
print("   - '✅ Final GLB exported'")

print("\nExpected Result:")
print("- 3D view should show a door with a rectangular cut")
print("- Console should show successful sweep operation messages")
print("- No red wireframe issues")

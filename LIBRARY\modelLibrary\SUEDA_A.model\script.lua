-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  xLimit = 247
  yLimit = 247
  xMin = 140
  yMin = 140
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local offset = 40
  local notchWidth = 1.5
  local notchDepth = 5
  local finalDepth = 10
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenWidth = finalDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  local cThickDiameter = 20
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.set<PERSON>ayer("K_AciliV15")
  if Y>yLimit and X>xLimit then		--Middle shape
    G.setThickness(0)
    distance = 3*notchWidth+offset
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.sunkenFrame(point1,point2,finalDepth,vWideAngle,vWideDiameter)
  end
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-(notchDepth+2))
  distance = notchWidth		--Outer notches
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setThickness(-(notchDepth+1))
  distance = 3*notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_Freze20mm")
  if Y>yLimit and X>xLimit then		--Depth cleaning
    G.setThickness(-(notchDepth))
    distance = 3*notchWidth
    point1 = {distance, distance}
    point2 = {X-distance, Y-distance}
    G.rectangle(point1,point2)
    
    if offset+sunkenWidth/2 > cThickDiameter then
      distance = 3*notchWidth+offset+sunkenWidth/2
      point3 = {distance, distance}
      point4 = {X-distance, Y-distance}
      G.rectangle(point3,point4) 
    
      k = (offset+sunkenWidth/2)/(cThickDiameter/2)
      for i=1, k, 1 do
        point1 = G.ptAdd(point1,{cThickDiameter/2,cThickDiameter/2})
        point2 = G.ptSubtract(point2, {cThickDiameter/2,cThickDiameter/2})
        if point1[1]>point3[1] then
          break
        end
        G.rectangle(point1,point2)
      end
    end
  else
    G.setLayer("Cep_Acma")		--Pocketing
    G.setThickness(-(notchDepth))
    distance = 3*notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
  end
  return true
end

require "ADekoDebugMode"
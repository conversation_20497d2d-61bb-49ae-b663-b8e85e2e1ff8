-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 30
  b = 100
  
  G = ADekoLib
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
 
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (X>200 and Y>200) then
    <PERSON><PERSON>setLayer("K_Top20mm")
    G.setThickness(-5)
    G.rectangle({b, b}, {X-b, Y-b})
  end
  
  <PERSON><PERSON>set<PERSON>ayer("K_Top10mm")
    G.setThickness(-5)
  local p = {}
  p[1]  = {a, a}
  p[2]  = {2*a, a}
  p[3]  = {2*a, Y-a}
  p[4]  = {a, Y-a}
  p[5]  = {a, Y-2*a}
  p[6]  = {X-a, Y-2*a}
  p[7]  = {X-a, Y-a}
  p[8]  = {X-2*a, Y-a}
  p[9]  = {X-2*a, a}
  p[10] = {X-a, a}
  p[11] = {X-a, 2*a}
  p[12] = {a, 2*a}
  p[13] = G.deepcopy(p[1])
  G.polylineimp(p)
  
  return true
end

require "ADekoDebugMode"
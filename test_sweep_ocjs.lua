-- Test Sweep Operation with OpenCascade.js Integration
-- This script creates geometry that can be processed by the OCJS worker

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(200, 150, 18)

print("=== Testing Sweep Operation with OCJS ===")

-- Test 1: Create a simple door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 200, 150)

-- Test 2: Create tool profiles for sweeping
print("2. Creating tool profiles...")

-- Create a simple rectangular groove (like a router bit path)
AdekoLib.layer("8MM")  -- 8mm endmill
AdekoLib.rect("groove1", 20, 20, 160, 30)  -- Horizontal groove

-- Create a circular pocket
AdekoLib.layer("6MM")  -- 6mm endmill  
AdekoLib.circle("pocket1", 100, 75, 15)    -- Circular pocket in center

-- Create a linear toolpath
AdekoLib.layer("4MM")  -- 4mm endmill
AdekoLib.line("line1", 30, 100, 170, 100)  -- Horizontal line cut

-- Test 3: Create more complex shapes for sweep testing
print("3. Creating complex sweep profiles...")

-- Create a decorative edge profile
AdekoLib.layer("V120")  -- V-bit tool
AdekoLib.polyline("decorative_edge", {
    {10, 10}, {190, 10}, {190, 140}, {10, 140}, {10, 10}
})

-- Create multiple depth cuts
AdekoLib.layer("12MM")  -- 12mm endmill for roughing
AdekoLib.rect("rough_pocket", 50, 50, 100, 50)

-- Test 4: Create overlapping operations to test boolean operations
print("4. Creating overlapping operations...")

AdekoLib.layer("10MM")  -- 10mm endmill
AdekoLib.circle("overlap1", 80, 60, 20)
AdekoLib.circle("overlap2", 120, 60, 20)

-- Test 5: Create edge operations
print("5. Creating edge operations...")

AdekoLib.layer("EDGE6MM")  -- Edge profiling
AdekoLib.rect("edge_profile", -5, -5, 210, 160)  -- Slightly oversized for edge cutting

-- Test 6: Create drilling operations
print("6. Creating drilling operations...")

AdekoLib.layer("DRILL8MM")  -- 8mm drill
AdekoLib.circle("hole1", 40, 40, 4)
AdekoLib.circle("hole2", 160, 40, 4)
AdekoLib.circle("hole3", 40, 110, 4)
AdekoLib.circle("hole4", 160, 110, 4)

-- Test 7: Create chamfer operations
print("7. Creating chamfer operations...")

AdekoLib.layer("CHAMFER")  -- Chamfer tool
AdekoLib.rect("chamfer_rect", 70, 70, 60, 30)

print("8. Test geometry created successfully!")
print("   - Door panel: 200x150x18mm")
print("   - Tool operations: 8MM, 6MM, 4MM, V120, 12MM, 10MM, EDGE6MM, DRILL8MM, CHAMFER")
print("   - Shapes: rectangles, circles, lines, polylines")
print("   - Ready for OCJS sweep processing")

-- Finish the model
AdekoLib.finish()

print("\n=== Sweep Test Geometry Complete ===")
print("This script creates various geometric shapes that can be used to test:")
print("1. Door body creation from PANEL layer")
print("2. Tool BRep generation for different tool types")
print("3. Sweep operations along tool paths")
print("4. Boolean operations (subtract, union, intersect)")
print("5. Complex multi-tool operations")
print("\nTo test the sweep operation:")
print("1. Run this script in the application")
print("2. Use the 3D visualization to see the generated geometry")
print("3. The OCJS worker will process the sweep operations")
print("4. Check the console for sweep operation results")

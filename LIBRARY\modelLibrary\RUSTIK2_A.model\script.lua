-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  a = 50
  n = 2
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local r = 0.24
  local depth = 8
  local origX = X
  local origY = Y
  X = 500
  Y = 700
  
  if origX>250 and origY>250 then
    <PERSON><PERSON>set<PERSON>ayer("K_Kut20mm_V30")
    G.setThickness(-depth)
    local p1 = {a, Y}
    local p2 = {X/2, Y-(1-2*r)*X}
    local p3 = {X-a, Y}
    comment, dp12, p12 = G.circleLineIntersection(p1, r*X, p1, p2)
    comment, p23, dp23 = G.circleLineIntersection(p3, r*X, p2, p3)
    local rP2 = G.distance(p2, p12)
    comment, tepe, dik2 = G.circleLineIntersection(p2, rP2, p2, {p2[1], Y})
    local bulge = G.bulge(p12, tepe, p23)
    --G.line(p12, p23, bulge)		--Middle line
    p22 = G.ptAdd(p2, {-X/4, 0})
    p222 = G.ptAdd(p2, {X/4, 0})
    comment, dtP1, tP1 = G.circleLineIntersection(p1, r*X, p1, p22)
    comment, tP3, dtP3 = G.circleLineIntersection(p3, r*X, p3, p222)
    local corner1, corner2 = {a, Y-r*X}, {r*X, Y}
    bulge = G.bulge(corner1, tP1, p12)
    --G.line(corner1, p12, bulge)		--Left line
    local m1, m2 = {X-a, Y-r*X}, {X-r*X-a, Y}
    bulge = G.bulge(m1, tP3, p23)
    --G.line(m1, p23, bulge)		--Right Line
    
    local pointsOrta = G.circularArc(p2, 2*rP2, 500, 56, 127)
    local pointsSol  = G.circularArc(p1, 2*r*X, 100, 270, 305)
    local pointsSag  = G.circularArc(p3, 2*r*X, 100, 230, 270)
  
    local arcs  = G.joinPolylines(G.joinPolylines(pointsOrta, pointsSag), pointsSol)
    local scaledArcsTop = G.scaleVertical(G.scaleHorizontal(arcs, 1.1*a, origX-1.1*a), origY-n*a, origY-a)
    local final = G.joinPolylines(scaledArcsTop, {{origX-a, origY-n*a}, {origX-a, a}, {a, a}, {a, origY-n*a}, scaledArcsTop[1]})
    G.polylineimp(final)
    
    G.showPar({0,a},{a,a},"a")
    G.showPar(p1,corner1,"n*a")
  end

  return true
end

require "ADekoDebugMode"
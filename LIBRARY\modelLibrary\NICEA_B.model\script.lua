-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.make<PERSON>art<PERSON>hape()
  
  xMin = 140
  yMin = 140
  
  a = 50
  c = 5
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  local notchDepth = 2
  local finalDepth = 8
  local cMidDiameter = 10
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenWidth = finalDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  local bulge = G.bulge({X-c-a, Y-c-1.5*a, 0, 0}, {X/2, Y-c-a}, {a, Y-c-1.5*a})
  local points = {{X-c-a, Y-c-1.5*a, 0, bulge},{c+a, Y-c-1.5*a, 0, 0},{c+a, c+1.5*a, 0, bulge},{X-c-a, c+1.5*a},{X-c-a, Y-c-1.5*a, 0, bulge}}
  
  G.setLayer("H_Freze10mm_Dis")
  G.setThickness(-notchDepth)
  G.rectangle ({c,c},{X-c,Y-c})
  
  G.setLayer("K_AciliV30")
  G.setThickness(-finalDepth)
  G.sunkenFrameAny(points, 30,finalDepth,vWideAngle,vWideDiameter)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(G.offSet(points, -sunkenWidth))
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,(10)))
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points, -sunkenWidth))
  return true
end

------------------------------------------------
require "ADekoDebugMode"
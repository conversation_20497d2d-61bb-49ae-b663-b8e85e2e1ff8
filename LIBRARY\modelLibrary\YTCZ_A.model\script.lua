-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  xMin = 140
  yMin = 140
  blockY = 80
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2.5
  local finalDepth = 5
  local stepY = math.floor(Y/ blockY)
  local correctedBlockY = Y / stepY
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  point1 = {0,Y}
  point2 = {X,0}
  
  for i=1,stepY-1 do
    G.setLayer("K_AciliV60")
    G.setThickness(-finalDepth)
    local y = Y-i*correctedBlockY
    point1 = {0,Y}
    point2 = {X,0}  
    G.line(point1,<PERSON>.ptAdd(point1,point2),0)
    
    <PERSON><PERSON>set<PERSON>ayer("K_AciliV30")
    G.setThickness(-notchDepth)
    G.line(point1,G.ptAdd(point1,point2),0)
  end
  G.rectangle({0,0}, {X,Y})

  return true
end

require "ADekoDebugMode"